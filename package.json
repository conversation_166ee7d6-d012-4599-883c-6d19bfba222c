{"name": "zb-svelte", "version": "0.0.1", "scripts": {"dev": "vite dev --host", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "paraglide": "paraglide-js compile --project ./project.inlang --watch", "postinstall": "patch-package"}, "devDependencies": {"@fontsource/fira-mono": "^4.5.10", "@iconify-json/solar": "^1.1.8", "@inlang/cli": "^2.18.1", "@inlang/paraglide-js": "1.11.2", "@inlang/paraglide-js-adapter-vite": "^1.0.1", "@inlang/plugin-message-format": "^2.0.0", "@melt-ui/pp": "^0.1.4", "@melt-ui/svelte": "^0.65.1", "@neoconfetti/svelte": "^1.0.0", "@sveltejs/adapter-node": "^2.0.2", "@sveltejs/adapter-static": "^3.0.1", "@sveltejs/enhanced-img": "^0.1.5", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@tailwindcss/typography": "^0.5.10", "@types/cookie": "^0.5.1", "@types/dompurify": "^3.0.5", "@types/node": "^20.10.3", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.16", "clsx": "^2.0.0", "eslint": "^8.28.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-svelte": "^2.30.0", "flowbite": "^2.2.0", "flowbite-svelte": "^0.44.20", "patch-package": "^8.0.0", "postcss": "^8.4.31", "prettier": "^3.1.0", "prettier-plugin-svelte": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.7", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "svelte-preprocess-import-assets": "^1.1.0", "svelte-sequential-preprocessor": "^2.0.1", "sveltekit-superforms": "^1.11.0", "tailwindcss": "^3.3.5", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^5.0.0", "zod": "^3.22.4"}, "type": "module", "dependencies": {"@directus/sdk": "^13.0.2", "@fontsource-variable/lexend": "^5.0.18", "@fontsource/lexend": "^5.0.17", "@fontsource/rubik": "^5.1.0", "@inlang/paraglide-sveltekit": "0.11.0", "@splidejs/splide": "^4.1.4", "@splidejs/svelte-splide": "^0.2.9", "caniuse-lite": "^1.0.30001718", "dompurify": "^3.0.6", "motion": "^10.16.4", "tailwind-hamburgers": "^1.3.5", "unplugin-icons": "^0.18.0", "vanilla-cookieconsent": "^3.0.0"}}