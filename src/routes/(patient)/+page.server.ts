import directus from '$lib/utils/directus'
import { readItems } from '@directus/sdk';
import { error } from '@sveltejs/kit';
export async function load() {

    try {
      const pages = await directus.request(
        readItems('articles', {
          sort: ['sort', '-date_created'],
          fields: ['id', 'title', 'cover', 'excerpt', 'status', 'date_created', { translations: ['*'] }]
        })
      );
        
      //console.log(pages);

      if (pages) {
        return {
          pages
        };
      } else {
        error(404);
      }

    } catch (err) {
      error(404);
    }
}