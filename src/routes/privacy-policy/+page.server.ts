import directus from '$lib/utils/directus'
import { readItems } from '@directus/sdk';
import { error } from '@sveltejs/kit';
import { languageTag } from '$lib/paraglide/runtime'; 

export async function load(params) {
    const lang = languageTag();
    try {
      const pages = await directus.request(
        //@ts-ignore
        readItems('legals', {
          filter: {
            slug: {
              _eq: 'polityka-prywatnosci',
            },
          },
          deep: {
            translations: {
              _filter: {
                languages_code: { _eq: lang },
              },
            },
          },
          fields: ['*', { translations: ['*'] }],
          limit: 1,
        })
      );
        
      const page = pages[0].translations[0];
      //console.log(page);

      if (page) {
        return {
          page
        };
      } else {
        error(404);
      }

    } catch (err) {
      error(404);
    }
}