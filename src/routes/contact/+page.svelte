<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	import { createEventDispatcher } from 'svelte';
	import Carousel from "$lib/components/Carousel.svelte";

	const dispatcher = createEventDispatcher();

	let name = '';
	let email = '';
	let phone = '';
	let topic = '';
	let message = '';

	let formSubmitted = false;

	async function handleSubmit() {
		formSubmitted = true;
		const leadData = { name, email, phone, topic, message };
		const response = await fetch('https://zbcms.zbadani.pl/items/Leads', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(leadData)
		});
	}

</script>

<svelte:head>
	<title>Zbadani.pl | {m.contact()}</title>
	<meta name="description" content="Kontakt" />
</svelte:head>

<div class="container mx-auto mb-12 lg:flex gap-8 items-start">
	<div class="bg-blue-900 p-12 mb-12 text-left rounded-2xl w-full">
		<h1 class="text-left text-2xl mb-4 text-blue-300">{m.c_contact_with_us()}</h1>
		<p>{m.c_questions()}</p><br />
		<p>{m.c_synektikgroup()}<br />
		Józefa Piusa Dziekońskiego 3<br />
		00-728 {m.c_warsaw()}</p>
	</div>
	<div class="w-full">

{#if !formSubmitted}
<form on:submit|preventDefault={handleSubmit}>
  <label>
    {m.c_name()}:
    <input type="text" bind:value={name} />
  </label>
  <label>
    {m.c_email()}:
    <input type="email" bind:value={email} />
  </label>
  <label>
    {m.c_phone()}:
    <input type="tel" bind:value={phone} />
  </label>
  <label>
    {m.c_subject()}:
    <select bind:value={topic}>
		<option value="Pomoc dla użytkownika Portalu Pacjenta" selected>{m.c_topic1()}</option>
		<option value="Zapytanie produktowe">{m.c_topic2()}</option>
		<option value="Oferta">{m.c_topic3()}</option>
		<option value="Prezentacja produktów">{m.c_topic4()}</option>
		<option value="Inne">{m.c_topic_other()}</option>
	</select>
  </label>
  <label>
    {m.c_message()}:
    <textarea bind:value={message}></textarea>
  </label>
  <button type="submit" class="btn">{m.c_send()}</button>
</form>
{:else}
<p class="text-lg text-blue-300">{m.c_thx()}</p>
{/if}

	</div>
</div>

<Carousel></Carousel>

<style lang="postcss">
	form label {
		@apply mb-4 block text-sm text-blue-300
	}
	form input[type="text"], form input[type="email"], form input[type="tel"], form textarea, select {
		@apply block w-full
	}
	input[type="text"], input[type="email"], form input[type="tel"], textarea, select {
		@apply p-2 border border-blue-700 rounded-md
	}
 .invalid {
    color: red;
  }
</style>