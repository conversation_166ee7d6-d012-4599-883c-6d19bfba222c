<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { browser } from '$app/environment';
  import Logo from '$lib/assets/images/logo-animated.svg';

  type Demo = {
    title: string;
    logoLink?: string;
    url: string;
    updateDate: string;
  };

  let demoData: Demo[] = [];
  let password = '';
  let error = false;
  let isAuthenticated = false;

  function checkAuth() {
    if (!browser) return false;
    
    const cookies = document.cookie.split(';').map(cookie => cookie.trim());
    const authCookie = cookies.find(c => c.startsWith('demoAuth='));
    
    if (!authCookie || !authCookie.includes('true')) {
      // Not authenticated, redirect to login
      goto('/demo');
      return false;
    }
    
    return true;
  }

  onMount(() => {
    isAuthenticated = checkAuth();
    
    if (isAuthenticated) {
      // Only fetch data if authenticated
      fetchDemoData();
    }
  });

  async function fetchDemoData() {
    try {
      const response = await fetch('/demoData.json');
      demoData = await response.json();
    } catch (error) {
      console.error('Error fetching demo data:', error);
    }
  }

  function logout() {
    document.cookie = 'demoAuth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    window.location.href = "/";
  }
</script>

<svelte:head>
    <title>Zbadani.pl | Prezentacja Demo</title>
    <meta name="robots" content="noindex, nofollow" />
</svelte:head>

<main class="min-h-screen py-16 bg-gray-50 bg-light">
    <div class="container mx-auto px-4 py-8">
        <div class="flex mb-12 items-center text-center">
          <img src={Logo} alt="Logo Zbadani.pl" width="167" height="50" />
          <h1 class="text-2xl font-medium text-center text-blue-200 pl-4 ml-4 border-l border-blue-200">Demo</h1>
          <button on:click={logout} class="ml-auto btn text-center h-12">Wyloguj</button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
            {#each demoData as demo}
                <a href={demo.url} target="_blank" class="bg-white shadow-xl hover:shadow-blue-500/20 shadow-blue-300/5 rounded-xl p-12 flex gap-4 items-center border-white border-2 hover:border-blue-500 transition-all">
                    <div>
                        {#if demo.logoLink}
                          <img src={demo.logoLink} alt={demo.title} class="h-16 mx-auto m-0" />
                        {/if}
                    </div>
                    <div>
                      <h2 class="text-md font-medium text-left m-0">{demo.title}</h2>
                      <p class="text-left text-gray-500 m-0 opacity-60">{demo.updateDate}</p>
                    </div>
                  </a>
            {/each}
        </div>
    </div>
</main>
