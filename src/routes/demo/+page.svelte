<script>
    import { goto } from '$app/navigation';
    import { browser } from '$app/environment';
    import logo<PERSON><PERSON><PERSON> from '$lib/assets/images/logo-animated.svg'

    let password = '';
    let error = false;
    let isLoading = false;

    // Hardcoded password for client-side validation
    // This is a temporary solution
    const DEMO_PASSWORD = 'Demo12!';

    async function handleSubmit() {
        isLoading = true;
        error = false;

        try {
            // Direct client-side validation
            if (password === DEMO_PASSWORD) {
                if (browser) {
                    // Set cookie with path to ensure it's available on all pages
                    document.cookie = 'demoAuth=true; path=/; max-age=3600';
                    
                    // Small delay to ensure cookie is set before navigation
                    setTimeout(() => {
                        goto('/demo/prezentacja');
                    }, 100);
                }
            } else {
                error = true;
                password = '';
            }
        } catch (err) {
            console.error('Login error:', err);
            error = true;
        } finally {
            isLoading = false;
        }
    }
</script>

<svelte:head>
    <title>Zbadani.pl | Demo - Strona dla handlowców</title>
    <meta name="robots" content="noindex, nofollow" />
</svelte:head>

<div class="flex items-center justify-center min-h-screen bg-light p-4">
    <div class="w-full max-w-md p-8 bg-white rounded-3xl shadow-2xl shadow-blue-300/10">
        <div class="flex justify-center mb-8">
            <img src="{logoZbadani}" alt="Zbadani.pl" class="h-16 w-auto" />
        </div>
        <p class="mb-8 text-center text-gray-600 text-md">
            Ta strona jest przeznaczona wyłącznie dla pracowników handlowych Zbadani.pl.<br />
            Aby uzyskać dostęp, wprowadź hasło.
        </p>

        <form on:submit|preventDefault={handleSubmit} class="space-y-6">
            <div>
                <label for="password" class="block mb-2 text-sm font-semibold text-gray-700">Hasło</label>
                <input
                    type="password"
                    id="password"
                    bind:value={password}
                    class="w-full px-5 py-3 border border-blue-700 rounded-lg text-md ring-0"
                    placeholder="Wprowadź hasło"
                    required
                />
                {#if error}
                    <p class="mt-2 text-sm text-red-500 text-md text-center bg-red-900 rounded-md py-2">Nieprawidłowe hasło. Spróbuj ponownie.</p>
                {/if}
            </div>
            <button
                type="submit"
                class="btn btn-fill w-full !py-3"
                disabled={isLoading}
            >
                {#if isLoading}
                    Logowanie...
                {:else}
                    Zaloguj się
                {/if}
            </button>
        </form>
    </div>
</div>
