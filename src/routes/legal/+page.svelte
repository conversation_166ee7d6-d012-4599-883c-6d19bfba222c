<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	import { PUBLIC_DIRECTUS_URL } from '$env/static/public'
	const PUBLIC_DIRECTUS = PUBLIC_DIRECTUS_URL;
	export let data;
</script>

<svelte:head>
	<title>Zbadani.pl | {data.page.title}</title>
	<meta name="description" content="Regulamin i polityka prywatności" />
</svelte:head>

<div class="container mx-auto">
	{#if data.page}
		<div class="bg-blue-900 p-16 mb-12 text-center rounded-2xl">
			<h1 class="text-center text-2xl text-blue-300">{data.page.title}</h1>
			<a href="{PUBLIC_DIRECTUS}/assets/{data.page.pdf}" target="_blank" class="btn mt-4">{m.open_pdf()}</a>
		</div>
		{#if data.page.content}<div class="html mb-24">{@html data.page.content}</div>{/if}
	{/if}
</div>

<style lang="postcss">
	.html {
		@apply border border-blue-900 p-12 rounded-2xl shadow-lg
	}
	:global(.html p) {
		@apply text-black-100 leading-5 mb-4 text-sm
	}
	:global(.html h2) {
		@apply text-black-100 leading-5 mb-4 text-2xl
	}
	:global(.html h3) {
		@apply text-black-100 leading-5 mb-4 text-xl
	}
	:global(.html table) {
		@apply w-full;
	}
	:global(.html table tr td) {
		@apply border border-black-900 p-4
	}
</style>