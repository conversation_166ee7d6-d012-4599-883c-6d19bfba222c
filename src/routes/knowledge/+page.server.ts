/* export const load = async () => {
    try {
        const response = await fetch(`https://synektik.com.pl/wp-json/wp/v2/posts/?categories=46&per_page=3&orderby=date&order=desc`);
        
        if (!response.ok) {
            // If the response status is not okay, handle the error
            throw new Error(`Error: ${response.statusText}`);
        }

        const posts = await response.json();
        return { posts };
    } catch (error) {
        console.error('Error fetching data:', error);

        // Return an empty object or a default value in case of an error
        return { posts: [] };
    }
};
 */

import directus from '$lib/utils/directus'
import { readItems } from '@directus/sdk';
import { error } from '@sveltejs/kit';
export async function load() {

    try {
      const pages = await directus.request(
        readItems('articles', {
          fields: ['id', 'title', 'cover', 'excerpt', 'status', 'date_created', { translations: ['*'] }]
        })
      );
        
      //console.log(pages);

      if (pages) {
        return {
          pages
        };
      } else {
        error(404);
      }

    } catch (err) {
      error(404);
    }
}