<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	import PostCard from '$lib/components/PostCard.svelte';
	export let data;
</script>
<svelte:head>
	<title>Zbadani.pl | {m.patient_patientZone()}</title>
	<meta name="description" content="Artykuły na zdrowie." />
</svelte:head>

{#if languageTag() == "pl"}
	<h1 class="text-3xl text-blue-300 text-center py-16 font-medium">{m.patient_patientZone()}</h1>
{:else}
	<h1 class="text-3xl text-blue-300 text-center pt-16 pb-4 font-medium">{m.patient_patientZone()}</h1>
	<h2 class="text-xl bg-orange-900 text-orange-300 text-center inline-block mx-auto py-2 px-2 rounded-lg mb-8 font-medium">{m.content_only_in_pl()}</h2>
{/if}
<div class="bg-blue-900 py-16">
	<div class="container mx-auto lg:grid lg:grid-cols-3 lg:gap-12 w-full">
		<PostCard posts={data.pages} />
	</div>
</div>