<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	import SinglePostCard from '$lib/components/SinglePostCard.svelte';
	export let data;
</script>
<svelte:head>
	<title>Zbadani.pl | {m.patient_health_guide()}</title>
	<meta name="description" content=".." />
</svelte:head>

<div class="w-full">
	{#if languageTag() != "pl"}
		<h2 class="w-full text-xl bg-orange-900 text-orange-300 text-center inline-block py-2 px-2 rounded-lg my-8 font-medium mx-auto">{m.content_only_in_pl()}</h2>
	{/if}
	<div class="container mx-auto">
		<SinglePostCard posts={data.pages} />
	</div>
</div>

<style lang="postcss">
	.breadcrumbs a {
		@apply hover:underline hover:text-blue-500 inline-block text-blue-500
	}
</style>