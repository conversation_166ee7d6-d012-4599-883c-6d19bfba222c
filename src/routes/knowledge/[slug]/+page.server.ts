import directus from '$lib/utils/directus'
import { readItems } from '@directus/sdk';
import { error } from '@sveltejs/kit';
export async function load(params) {

    try {
      const pages = await directus.request(
        readItems('articles', {
            filter: {
                id: {
                _eq: params.params.slug,
                },
            },
            fields: ['id', 'title', 'cover', 'content', 'status', 'date_created', { translations: ['*'] }],
            limit: 1
        })
      );
        
      //console.log(pages);

      if (pages) {
        return {
          pages
        };
      } else {
        error(404);
      }

    } catch (err) {
      error(404);
    }
}