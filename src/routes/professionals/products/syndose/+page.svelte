<script lang="ts">
	import Bg from '$lib/assets/images/syndose/bg2.webp'

	import Banner from "$lib/components/Banner.svelte";

	import LogoSynDose from '$lib/assets/images/logo_syndose_new.svg';

	import ImgKontrola from '$lib/assets/images/syndose2/image-features-placeholder.webp'
	import ImgChart from "$lib/assets/images/syndose2/image-feature-chart.webp";
	import ImgAlert from "$lib/assets/images/syndose2/image-feature-alert.webp";
	import ImgSim from "$lib/assets/images/syndose2/image-feature-sim.webp";
	import ImgOpt from "$lib/assets/images/syndose2/image-feature-opt.webp";
	import ImgDrl from "$lib/assets/images/syndose2/image-feature-drl.webp";
	import ImgDict from "$lib/assets/images/syndose2/image-feature-dict.webp";
	import ImgWeb from "$lib/assets/images/syndose2/icon-feature-web.svg";
	import ImgScreenshot from "$lib/assets/images/syndose2/image-screenshot.webp";

    import Breadcrumbs from "$lib/components/Breadcrumbs.svelte";
	import * as m from '$lib/paraglide/messages';
	import WhyChooseSection from '$lib/components/WhyChooseSection.svelte';
	import FinancingSection from '$lib/components/FinancingSection.svelte';
	import ProcessSection from '$lib/components/ProcessSection.svelte';
	import SeeAlsoSection from '$lib/components/SeeAlsoSection.svelte';
	import ContactSection from '$lib/components/ContactSection.svelte';

	import backBtn from '$lib/assets/images/back.svg';
	import forwardBtn from '$lib/assets/images/forward.svg';
	import closeBtn from '$lib/assets/images/close.svg';


	import KlientOM from '$lib/assets/images/klient-om.webp';
	import KlientNCM from '$lib/assets/images/klient-ncm.webp';
	import KlientSniadecja from '$lib/assets/images/klient-sniadecja.webp';
    import SyndoseAccordion from "$lib/components/SyndoseAccordion.svelte";
    import Carousel from '$lib/components/Carousel.svelte';

	let currentIndex = 0; // Index of the active slide
  	let isPopupVisible = false; // State for popup visibility
  	let expandedText = ""; // State for expanded text

	let slides = [
		{
			name: m.syndose_opinion_1_name(),
			institution: m.syndose_opinion_1_institution(),
			text: '"'+m.syndose_opinion_1_text(),
			textExpanded: '"'+m.syndose_opinion_1_expanded()+'"',
			image: KlientOM,
		},
		{
			name: m.syndose_opinion_2_name(),
			institution: m.syndose_opinion_2_institution(),
			text: '"'+m.syndose_opinion_2_text(),
			textExpanded: '"'+m.syndose_opinion_2_expanded()+'"',
			image: KlientNCM,
		},
		{
			name: m.syndose_opinion_3_name(),
			institution: m.syndose_opinion_3_institution(),
			text: '"'+m.syndose_opinion_3_text(),
			textExpanded: '"'+m.syndose_opinion_3_expanded()+'"',
			image: KlientSniadecja,
		},
	];

	const nextSlide = () => {
		currentIndex = (currentIndex + 1) % slides.length;
	};

	const prevSlide = () => {
		currentIndex = (currentIndex - 1 + slides.length) % slides.length;
	};

	const showPopup = (text: string) => {
		const formatText = (input: string) =>
			input
			.replace(/\b([aiouwzAIUOWZ])\s(?!&nbsp;)/g, '$1&nbsp;')
			.replace(/(\d+)\s([a-zA-ZęóąśłżźćńĘÓĄŚŁŻŹĆŃ]+)/g, '$1&nbsp;$2');

		expandedText = formatText(text);
		isPopupVisible = true;
	};

	const closePopup = () => {
		isPopupVisible = false;
	};

</script>

<svelte:head>
	<title>{m.syndose_page_title()}</title>
	<meta name="description" content={m.syndose_page_description()} />
</svelte:head>

<Banner src={Bg}>
	<div slot="bannerContent" class="text-blue-300">
		<Breadcrumbs current={m.syndose_breadcrumb()} previous="Dla profesjonalistów" previousUrl="/dla-profesjonalistow" />
		<div class="text-center flex mb-4 items-center">
			<img src={LogoSynDose} alt="Logo Syndose" class="max-h-32">
		</div>
		<p class="mb-4 text-blue-100">{m.syndose_banner_p1()}</p>
		<p>{m.syndose_banner_p2()}</p>
		<a href="#skontaktuj-sie-z-nami" class="btn btn-fill mt-4">{m.syndose_banner_btn()}</a>
	</div>
</Banner>

<div class="bg-blue-900">
	<main>

		<section id="dla-kogo" class="bg-blue-900 !pb-0">
			<div class="container mx-auto md:flex items-center md:gap-16">
				<div class="md:w-1/3">
					<h3>{m.syndose_for_whom_title()}</h3>
					<p>{m.syndose_for_whom_p()}</p>
					<ul class="list-disc list-outside pl-4">
						<li class="py-2">{m.syndose_for_whom_1()}</li>
						<li class="py-2">{m.syndose_for_whom_2()}</li>
						<li class="py-2">{m.syndose_for_whom_3()}</li>
						<li class="py-2">{m.syndose_for_whom_4()}</li>
						<li class="py-2">{m.syndose_for_whom_5()}</li>
					</ul>
					<a href="#skontaktuj-sie-z-nami" class="btn btn-fill mt-4 md:mb-0 mb-6">{m.syndose_for_whom_btn()}</a>
				</div>
				<div class="md:w-2/3 md:px-8">
					<img src="{ImgScreenshot}" alt="Zrzut ekranu" class="rounded-lg shadow-[0_35px_60px_-15px_rgba(0,42,163,0.1)]">
				</div>
			</div>
		</section>

		<section id="kluczowe-funkcje" class="bg-blue-900">
			<h3 class="mb-12 text-center">{m.syndose_key_features()}</h3>

			<div class="mx-auto container">
				<div class="md:grid grid-cols-4 grid-rows-9 gap-6 features-grid">
					<div class="row-span-2">
						<h4>{m.syndose_feature_reports()}</h4>
						<p>{m.syndose_feature_reports_desc()}</p>
					</div>
					<div class="row-span-2 col-start-1 row-start-3 bluey flex flex-col">
						<h5 class="opacity-60 text-sm">{m.syndose_feature_nuclear_new()}</h5>
						<h4>{m.syndose_feature_nuclear()}</h4>
						<p>{m.syndose_feature_nuclear_desc()}</p>
					</div>
					<div class="row-span-2 col-start-1 row-start-5">
						<h4>{m.syndose_feature_calculators()}</h4>
						<p>{m.syndose_feature_calculators_desc()}</p>
					</div>
					<div class="row-span-2 col-start-1 row-start-7 flex flex-col">
						<h4>{m.syndose_feature_remote()}</h4>
						<img class="mt-auto w-12" src="{ImgWeb}" alt="">
					</div>
					<div class="col-start-1 row-start-9">
						<h4>300 000+</h4>
						<p>{m.syndose_feature_processed()}</p>
					</div>
					<div class="row-span-4 col-start-2 row-start-1 flex flex-col">
						<h4>{m.syndose_feature_limits()}</h4>
						<p>{m.syndose_feature_limits_desc()}</p>
						<img src="{ImgDrl}" alt="DRL" class="py-6 px-6 mt-auto">
					</div>
					<div class="row-span-4 col-start-2 row-start-5 nopadding">
						<img src="{ImgKontrola}" alt="Kontrola dawek">
					</div>
					<div class="col-start-2 row-start-9">
						<h4>300+</h4>
						<p>{m.syndose_feature_connected()}</p>
					</div>
					<div class="row-span-4 col-start-3 row-start-1 bluey flex flex-col">
						<h4>{m.syndose_feature_visualization()}</h4>
						<p>{m.syndose_feature_visualization_desc()}</p>
						<img src="{ImgChart}" alt="Wykresy" class="mt-auto">
					</div>
					<div class="col-span-2 row-span-2 col-start-3 row-start-5 md:flex md:flex-row">
						<div class="md:w-1/2 md:flex md:flex-col">
							<h4>{m.syndose_feature_dictionaries()}</h4>
							<a class="mt-auto text-blue-300 underline" href="#zgodnie-z-prawem">{m.syndose_feature_dictionaries_link()}</a>
						</div>
						<div class="md:w-1/2 md:flex items-center"><img src="{ImgDict}" class="p-8" alt="Słowniki"></div>
					</div>
					<div class="row-span-3 col-start-3 row-start-7 bluey flex flex-col">
						<div>
							<h4>{m.syndose_feature_alerts()}</h4>
							<p>{m.syndose_feature_alerts_desc()}</p>
						</div>
						<div class="flex items-center h-full">
							<img src="{ImgAlert}" class="max-w-24 mx-auto" alt="Alerty">
						</div>
					</div>
					<div class="row-span-2 col-start-4 row-start-1 flex">
						<div>
							<h4>{m.syndose_feature_simulation()}</h4>
							<p>{m.syndose_feature_simulation_desc()}</p>
						</div>
						<img src="{ImgSim}" alt="Symulacja" class="ml-auto w-24 h-auto object-contain">
					</div>
					<div class="row-span-2 col-start-4 row-start-3 flex flex-col">
						<h4>{m.syndose_feature_optimization()}</h4>
						<img src="{ImgOpt}" alt="Optymalizacja" class="mt-auto">
					</div>
					<div class="col-start-4 row-start-7 flex items-center">
						<h5>{m.syndose_feature_access_historical()}</h5>
					</div>
					<div class="col-start-4 row-start-8 flex items-center">
						<h5>{m.syndose_feature_multi_facility()}</h5>
					</div>
					<div class="col-start-4 row-start-9 flex items-center">
						<h5>{m.syndose_feature_easy_config()}</h5>
					</div>
				</div>
			</div>

			<div class="container mx-auto pt-8"><p class="text-sm opacity-60">{m.syndose_stats_note()}</p></div>

		</section>

		<section id="zgodnie-z-prawem" class="bg-white">

			<div class="md:flex mx-auto container">
				<div class="md:w-1/2 mx-auto text-center relative">
					<h2 class="prehead">{m.syndose_law_section_title()}</h2>
					<h3 class="mb-8">{m.syndose_law_section_subtitle()}</h3>
				</div>
			</div>

			<div class="md:grid grid-cols-2 mx-auto container law">
				<div class="flex flex-col">
					<h4>{m.syndose_law_1_title()}</h4>
					<p>{m.syndose_law_1_desc()}</p>
					<a target="_blank" class="pt-2 mt-auto" href="https://dziennikustaw.gov.pl/D2023000019501.pdf">{m.syndose_law_1_source()}</a>
				</div>
				<div class="flex flex-col">
					<h4>{m.syndose_law_2_title()}</h4>
					<p>{m.syndose_law_2_desc()}</p>
					<a target="_blank" class="pt-2 mt-auto" href="https://dziennikustaw.gov.pl/D2022000270001.pdf">{m.syndose_law_1_source()}</a>
				</div>
				<div class="flex flex-col">
					<h4>{m.syndose_law_3_title()}</h4>
					<p>{m.syndose_law_3_desc()}</p>
					<a target="_blank" class="pt-2 mt-auto" href="https://isap.sejm.gov.pl/isap.nsf/download.xsp/WDU20230001173/O/D20231173.pdf">{m.syndose_law_1_source()}</a>
				</div>
				<div class="flex flex-col">
					<h4>{m.syndose_law_4_title()}</h4>
					<p>{m.syndose_law_4_desc()}</p>
					<a target="_blank" class="pt-2 mt-auto" href="https://dziennikustaw.gov.pl/D2022000262601.pdf">{m.syndose_law_1_source()}</a>
				</div>
				<div class="flex flex-col">
					<h4>{m.syndose_law_5_title()}</h4>
					<p>{m.syndose_law_5_desc()}</p>
					<a target="_blank" class="pt-2 mt-auto" href="https://dziennikustaw.gov.pl/D2022000268301.pdf">{m.syndose_law_1_source()}</a>
				</div>
				<div class="flex flex-col">
					<h4>{m.syndose_law_6_title()}</h4>
					<p>{m.syndose_law_6_desc()}</p>
					<a target="_blank" class="pt-2 mt-auto" href="https://isap.sejm.gov.pl/isap.nsf/download.xsp/WDU20230001173/O/D20231173.pdf">{m.syndose_law_1_source()}</a>
				</div>
			</div>

		</section>

		<section id="faq" class="bg-white !pt-0 !mt-0">
			<div class="flex mx-auto container">
				<div class="md:w-1/2 mx-auto text-center relative">
					<h2 class="prehead">{m.wdm_faq_title()}</h2>
					<h3 class="mb-0 pb-0">{m.wdm_faq_subtitle()}</h3>
					<svelte:component this={SyndoseAccordion} />
				</div>
			</div>
		</section>

		<section id="co-mowia" class="bg-blue-900">
			<div class="flex mx-auto container">
				<div class="md:w-1/2 mx-auto text-center relative">
					<h2 class="prehead">{m.syndose_opinions_title()}</h2>
					<h3 class="mb-8">{m.syndose_opinions_subtitle()}</h3>

					<div class="relative">
					  <!-- Slider Container -->
					  <div class="overflow-hidden rounded-2xl">
						<div class="md:flex transition-transform duration-300" style="transform: translateX(-{currentIndex * 100}%);">
						  {#each slides as slide, i}
							<div class="min-w-full bg-white p-12 text-left slider-item">
							  <div class="md:flex justify-between items-start">
								<div>
								  <p class="text-xl !mb-0">{slide.name}</p>
								  <p>{slide.institution}</p>
								</div>
								<img src={slide.image} class="max-h-16 my-4 md:my-0" alt="Client logo">
							  </div>
							  <p class="mt-4">{slide.text}</p>
							  <button class="underline mt-2" on:click={() => showPopup(slide.textExpanded)}>{m.syndose_opinion_show_more()}</button>
							</div>
						  {/each}
						</div>
					  </div>

					  <!-- Navigation -->
					  <button
						class="hidden md:block absolute top-1/2 left-[-1.5rem] transform -translate-y-1/2 bg-gray-200 p-2 rounded-full hover:opacity-50"
						on:click={prevSlide}
					  >
						<img class="border-white border-2 rounded-full" src={backBtn} alt="Poprzednia opinia">
					  </button>
					  <button
						class="hidden md:block absolute top-1/2 right-[-1.5rem] transform -translate-y-1/2 bg-gray-200 p-2 rounded-full hover:opacity-50"
						on:click={nextSlide}
					  >
						<img class="border-white border-2 rounded-full" src={forwardBtn} alt="Następna opinia">
					  </button>
					</div>

					<!-- Popup -->
					{#if isPopupVisible}
						<div class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
						<div class="bg-white p-6 md:rounded-2xl md:w-1/2 max-h-screen overflow-y-scroll py-8 text-left md:shadow-xl">
							<button class="mt-4 underline text-blue-600" on:click={closePopup}>
								<img src="{closeBtn}" class="w-8 h-8 mb-4" alt="Zamknij">
							</button>
							<p class="text-justify">{@html expandedText}</p>
						</div>
						</div>
					{/if}

				</div>

			</div>
		</section>
		<WhyChooseSection />
		<FinancingSection />
		<ProcessSection />

		<ContactSection product="syndose" />

		<SeeAlsoSection currentProduct="syndose" />

		<Carousel></Carousel>

	</main>
</div>

<style lang="postcss">
	.features-grid > div {
		@apply bg-white rounded-2xl p-6;
		box-shadow: 0px 12px 24px 0px rgba(125, 153, 232, 0.08);
	}
	.features-grid div.bluey {
		background: linear-gradient(180deg, #0848FF 0%, #0039D6 100%);
		box-shadow: none;
	}
	.features-grid > div.nopadding {
		@apply p-0 overflow-hidden;
	}

	.features-grid > div.nopadding img {
		height: 100%;
		object-fit: cover;
	}
	.features-grid > div h4 {
		@apply text-blue-300 mb-2;
	}
	.features-grid > div h5 {
		@apply text-blue-300;
	}
	.features-grid > div.bluey h4, .features-grid > div.bluey h5, .features-grid > div.bluey p {
		@apply text-white;
	}
	section ul {
		@apply text-blue-300;
	}
	section p {
		@apply text-blue-300 mb-4;
	}
	section h2 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h2.prehead {
		font-size: 1.3125rem;
		@apply text-blue-300;
	}
	section h3 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h4 {
		font-size: 1.3125rem;
		@apply text-blue-300 font-normal;
	}
	section {
		@apply py-32;
	}
	.law {
		@apply gap-6;
	}
	.law div {
		@apply px-6 py-4 rounded-2xl;
		border: 1px solid #BECDF8;
	}
	.law h4 {
		@apply text-base leading-5 mb-2;
	}
	.law p {
		@apply text-sm opacity-75 mb-0 pb-0;
	}
	.law a {
		@apply text-sm text-blue-500 underline;
	}
	#kluczowe-funkcje p {
		@apply mb-0 pb-0;
	}
	@media screen and (max-width: 1024px) {
		section {
			@apply py-8;
		}
		h3 br, h4 br {
			@apply hidden;
		}
		#kluczowe-funkcje div > div {
			@apply mb-6
		}
		.slider-item {
			@apply mb-8 rounded-2xl;
		}
		.law > div {
			@apply mb-6;
		}
		.products > div {
			@apply border p-8 rounded-2xl mb-6;
			border: 1px solid #BECDF8;
		}
	}
</style>
