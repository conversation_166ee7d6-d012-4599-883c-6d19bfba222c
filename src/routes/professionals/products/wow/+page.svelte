<script lang="ts">
	import Bg from '$lib/assets/images/wow/bg2.webp'

	import Banner from "$lib/components/Banner.svelte";

	import LogoWow from '$lib/assets/images/wow/logo_wow.svg';
	import ImgWow from '$lib/assets/images/wow/image-features-placeholder.webp'

	import Icon1 from "$lib/assets/images/wow/icon-1.svg";
	import Icon2 from "$lib/assets/images/wow/icon-2.svg";
	import Icon3 from "$lib/assets/images/wow/icon-3.svg";
	import Icon4 from "$lib/assets/images/wow/icon-4.svg";

	import IconS1 from "$lib/assets/images/wow/icon-s-1.svg";
	import IconS2 from "$lib/assets/images/wow/icon-s-2.svg";
	import IconS3 from "$lib/assets/images/wow/icon-s-3.svg";
	import IconS4 from "$lib/assets/images/wow/icon-s-4.svg";
	import IconS5 from "$lib/assets/images/wow/icon-s-5.svg";
	import IconS6 from "$lib/assets/images/wow/icon-s-6.svg";

	import IconOther from "$lib/assets/images/wow/icon-other.svg";

	import ImgScreenshot from "$lib/assets/images/wow/image-screenshot.webp";
	import ImgDICOM from '$lib/assets/images/wow/img-dicom.webp';
	import IconSMS from '$lib/assets/images/wow/icon-sms.svg'
	import IconRWD from '$lib/assets/images/wow/icon-rwd.svg'

    import Breadcrumbs from "$lib/components/Breadcrumbs.svelte";
	import * as m from '$lib/paraglide/messages';
	import WhyChooseSection from '$lib/components/WhyChooseSection.svelte';
	import FinancingSection from '$lib/components/FinancingSection.svelte';
	import ProcessSection from '$lib/components/ProcessSection.svelte';
	import SeeAlsoSection from '$lib/components/SeeAlsoSection.svelte';
	import ContactSection from '$lib/components/ContactSection.svelte';

    import WowAccordion from "$lib/components/WowAccordion.svelte";
    import Carousel from '$lib/components/Carousel.svelte';

</script>

<svelte:head>
	<title>{m.wow_page_title()}</title>
	<meta name="description" content={m.wow_page_description()} />
</svelte:head>

<Banner src={Bg}>
	<div slot="bannerContent" class="text-blue-300">
		<Breadcrumbs current={m.wow_breadcrumb()} previous={m.forMf()} previousUrl="/dla-profesjonalistow" />
		<div class="text-center flex mb-4 items-center">
			<img src={LogoWow} alt="Logo WOW" class="max-h-32">
		</div>
		<p class="mb-4 text-blue-100">{m.wow_banner_desc()}</p>
		<a href="#skontaktuj-sie-z-nami" class="btn btn-fill mt-4">{m.wow_banner_btn()}</a>
	</div>
</Banner>

<div class="bg-blue-900">
	<main>

		<section id="dla-kogo" class="bg-blue-900 !pb-0">
			<div class="container mx-auto md:flex items-center md:gap-16">
				<div class="md:w-1/2">
					<h3 class="mb-4">{m.wow_benefits_title()}</h3>
					<ul class="list-inside">
						<li class="py-3 flex gap-4 items-center">
							<img class="w-8 h-8" src="{Icon1}" alt="">
							<p class="!mb-0 !pb-0">{m.wow_benefit_1()}</p></li>
						<li class="py-3 flex gap-4 items-center">
							<img class="w-8 h-8" src="{Icon2}" alt="">
							<p class="!mb-0 !pb-0">{m.wow_benefit_2()}</p></li>
						<li class="py-3 flex gap-4 items-center">
							<img class="w-8 h-8" src="{Icon3}" alt="">
							<p class="!mb-0 !pb-0">{m.wow_benefit_3()}</p></li>
						<li class="py-3 flex gap-4 items-center">
							<img class="w-8 h-8" src="{Icon4}" alt="">
							<p class="!mb-0 !pb-0">{m.wow_benefit_4()}</p></li>
					</ul>
				</div>
				<div class="md:w-1/2 md:px-8">
					<img src="{ImgScreenshot}" alt="Zrzut ekranu" class="rounded-lg shadow-[0_35px_60px_-15px_rgba(0,42,163,0.1)]">
				</div>
			</div>
		</section>

		<section id="kluczowe-funkcje" class="bg-blue-900">
			<h3 class="mb-12 text-center">{m.syndose_key_features()}</h3>

			<div class="mx-auto container">
				<div class="md:grid grid-cols-4 grid-rows-7 gap-6 features-grid">
					<div class="row-span-2">
						<h4>{m.wow_feature_1_title()}</h4>
						<p>{m.wow_feature_1_desc()}</p>
					</div>
					<div class="row-span-4 nopadding">
						<img src="{ImgWow}" alt={m.wow_alt_result_pickup()}>
					</div>
					<div class="row-span-4 bluey flex flex-col">
						<div>
							<h4>{m.wow_feature_2_title()}</h4>
							<p>{m.wow_feature_2_desc()}</p>
							<p class="mt-4">{m.wow_feature_2_note()}</p>
						</div>
						<img src="{ImgDICOM}" alt={m.wow_alt_dicom()} class="mt-auto mix-blend-screen">
					</div>
					<div class="row-span-2 bluey flex flex-col">
						<h4>{m.wow_feature_3_title()}</h4>
						<div class="flex justify-center items-center h-full">
							<img src="{IconRWD}" alt={m.wow_alt_mobile_adapted()}>
						</div>
					</div>
					<div class="row-span-2 col-start-4 row-start-3">
						<h4>{m.wow_feature_4_title()}</h4>
						<p>{m.wow_feature_4_desc()}</p>
					</div>
					<div class="row-span-2 col-start-1 row-start-3">
						<h4>{m.wow_feature_5_title()}</h4>
						<p>{m.wow_feature_5_desc()}</p>
					</div>
					<div class="col-span-2 row-span-2 row-start-5 md:flex flex-row items-center">
						<div class="md:w-1/2">
							<h4>{m.wow_feature_6_title()}</h4>
							<p>{m.wow_feature_6_desc()}</p>
						</div>
						<div class="md:w-1/2 md:flex items-center justify-center">
							<img class="max-w-[200px]" src="{IconOther}" alt="">
						</div>
					</div>
					<div class="row-span-3 col-start-3 row-start-5 flex flex-col">
						<h4>{m.wow_feature_7_title()}</h4>
						<p>Powiadomienia SMS o dostepności opisu mogą być wysyłane z opóźnieniem, aby potencjalnie umożliwić lekarzowi zmianę opisu.</p>
						<div class="flex justify-center items-center h-full">
							<img src="{IconSMS}" alt={m.wow_alt_sms_notifications()}>
						</div>
					</div>
					<div class="row-span-3 col-start-4 row-start-5">
						<h4>{m.wow_feature_8_title()}</h4>
						<ul class="list-inside list-disc">
							<li>{m.wow_feature_8_1()}</li>
							<li>{m.wow_feature_8_2()}</li>
							<li>{m.wow_feature_8_3()}</li>
							<li>{m.wow_feature_8_4()}</li>
						</ul>
					</div>
					<div class="row-start-7">
						<h4 class="!mb-0 !pb-0">1200</h4>
						<p class="!py-0 !my-0">{m.wow_stats_1()}</p>
					</div>
					<div class="row-start-7">
						<h4 class="!mb-0 !pb-0">400</h4>
						<p class="!py-0 !my-0">{m.wow_stats_2()}</p>
					</div>
				</div>
		</div>

		</section>

		<section id="zgodnie-z-prawem" class="bg-white">

			<div class="md:flex flex-col mx-auto container">
				<div class="mx-auto text-center relative">
					<h2 class="prehead">{m.wow_how_it_works()}</h2>
					<h3 class="mb-8">{m.wow_how_it_works_subtitle()}</h3>
				</div>
				<div class="md:grid grid-cols-3 grid-rows-2 gap-12">
					<div>
						<span class="block w-16 h-16 rounded-full bg-[#EFF6FB] flex items-center justify-center">
							<img src="{IconS1}" alt={m.wow_alt_icon()}>
						</span>
						<h4 class="my-4"><span class="text-blue-500">1.</span> {m.wow_step_1_title()}</h4>
						<p class="text-blue-100">{m.wow_step_1_desc()}</p>
					</div>
					<div>
						<span class="block w-16 h-16 rounded-full bg-[#EFF6FB] flex items-center justify-center">
							<img src="{IconS2}" alt={m.wow_alt_icon()}>
						</span>
						<h4 class="my-4"><span class="text-blue-500">2.</span> {m.wow_how_it_works_step_2_title()}</h4>
						<p class="text-blue-100">{m.wow_how_it_works_step_2_desc()}</p>
					</div>
					<div>
						<span class="block w-16 h-16 rounded-full bg-[#EFF6FB] flex items-center justify-center">
							<img src="{IconS3}" alt={m.wow_alt_icon()}>
						</span>
						<h4 class="my-4"><span class="text-blue-500">3.</span> {m.wow_how_it_works_step_3_title()}</h4>
						<p class="text-blue-100">{m.wow_how_it_works_step_3_desc()}</p>
					</div>
					<div>
						<span class="block w-16 h-16 rounded-full bg-[#EFF6FB] flex items-center justify-center">
							<img src="{IconS4}" alt={m.wow_alt_icon()}>
						</span>
						<h4 class="my-4"><span class="text-blue-500">4.</span> {m.wow_how_it_works_step_4_title()}</h4>
						<p class="text-blue-100">{m.wow_how_it_works_step_4_desc()}</p>
					</div>
					<div>
						<span class="block w-16 h-16 rounded-full bg-[#EFF6FB] flex items-center justify-center">
							<img src="{IconS5}" alt={m.wow_alt_icon()}>
						</span>
						<h4 class="my-4"><span class="text-blue-500">5.</span> {m.wow_how_it_works_step_5_title()}</h4>
						<p class="text-blue-100">{m.wow_how_it_works_step_5_desc()}</p>
					</div>
					<div>
						<span class="block w-16 h-16 rounded-full bg-[#EFF6FB] flex items-center justify-center">
							<img src="{IconS6}" alt={m.wow_alt_icon()}>
						</span>
						<h4 class="my-4"><span class="text-blue-500">6.</span> {m.wow_how_it_works_step_6_title()}</h4>
						<p class="text-blue-100">{m.wow_how_it_works_step_6_desc()}</p>
					</div>
				</div>
			</div>

		</section>

		<section id="co-mowia" class="bg-blue-900">
			<div class="flex mx-auto container">
				<div class="md:w-1/2 mx-auto text-center relative">
					<h2 class="prehead">{m.wdm_faq_title()}</h2>
					<h3 class="mb-8">{m.wdm_faq_subtitle()}</h3>
					<svelte:component this={WowAccordion} />
				</div>
			</div>
		</section>

		<WhyChooseSection />

		<FinancingSection />

		<ProcessSection />

		<ContactSection product="wow" />

		<SeeAlsoSection currentProduct="wow" />

		<Carousel></Carousel>

	</main>
</div>

<style lang="postcss">
	.features-grid > div {
		@apply bg-white rounded-2xl p-6;
		box-shadow: 0px 12px 24px 0px rgba(125, 153, 232, 0.08);
	}
	.features-grid div.bluey {
		background: linear-gradient(180deg, #0848FF 0%, #0039D6 100%);
		box-shadow: none;
	}
	.features-grid > div.nopadding {
		@apply p-0 overflow-hidden;
	}

	.features-grid > div.nopadding img {
		height: 100%;
		object-fit: cover;
	}
	.features-grid > div h4 {
		@apply text-blue-300 mb-2;
	}
	.features-grid > div h5 {
		@apply text-blue-300;
	}
	.features-grid > div.bluey h4, .features-grid > div.bluey h5, .features-grid > div.bluey p {
		@apply text-white;
	}
	section ul {
		@apply text-blue-300;
	}
	section p {
		@apply text-blue-300 mb-4;
	}
	section h2 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h2.prehead {
		font-size: 1.3125rem;
		@apply text-blue-300;
	}
	section h3 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h4 {
		font-size: 1.3125rem;
		@apply text-blue-300 font-normal;
	}
	section {
		@apply py-32;
	}
	.law {
		@apply gap-6;
	}
	.law div {
		@apply px-6 py-4 rounded-2xl;
		border: 1px solid #BECDF8;
	}
	.law h4 {
		@apply text-base leading-5 mb-2;
	}
	.law p {
		@apply text-sm opacity-75 mb-0 pb-0;
	}
	.law a {
		@apply text-sm text-blue-500 underline;
	}
	#kluczowe-funkcje p {
		@apply mb-0 pb-0;
	}
	@media screen and (max-width: 1024px) {
		section {
			@apply py-8;
		}
		h3 br, h4 br {
			@apply hidden;
		}
		#kluczowe-funkcje div > div {
			@apply mb-6
		}
		.slider-item {
			@apply mb-8 rounded-2xl;
		}
		.law > div {
			@apply mb-6;
		}
		.products > div {
			@apply border p-8 rounded-2xl mb-6;
			border: 1px solid #BECDF8;
		}
	}
</style>