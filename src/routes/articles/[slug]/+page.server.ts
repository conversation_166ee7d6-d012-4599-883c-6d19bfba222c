import directus from '$lib/utils/directus'
import { readItems } from '@directus/sdk';
import { error } from '@sveltejs/kit';

export async function load(params) {

    try {
      const pages = await directus.request(
        readItems('pages', {
          filter: {
            slug: {
              _eq: params.params.slug,
            },
          },
          fields: ['*'],
          limit: 1,
        })
      );

      console.log('Hej!');

      console.log(pages);
        
      const page = pages[0];
      console.log(page);

      if (page) {
        return {
          page
        };
      } else {
        error(404);
      }

    } catch (err) {
      error(404);
    }
}