<script lang="ts">
  	import { availableLanguageTags } from "$lib/paraglide/runtime";
  	import { page } from "$app/stores";
	import { createEventDispatcher } from 'svelte';

	const dispatcher = createEventDispatcher();

	let name = '';
	let email = '';
	let phone = '';
	let topic = '';
	let message = '';

	let formSubmitted = false;

	async function handleSubmit() {
		formSubmitted = true;
		const leadData = { name, email, phone, topic, message };
		const response = await fetch('https://zbcms.zbadani.pl/items/Leads', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(leadData)
		});
	}

</script>

<svelte:head>
	<title>Zbadani.pl | Kontakt</title>
	<meta name="description" content="Wpisy" />
</svelte:head>

<div></div>