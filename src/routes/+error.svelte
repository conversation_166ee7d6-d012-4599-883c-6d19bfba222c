<script>
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { ParaglideJS } from "@inlang/paraglide-sveltekit"
	import { i18n } from "$lib/i18n"
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";

	onMount(() => {
		// Przekieruj wszystkie 404 na stronę główną
		if ($page.error && $page.status === 404) {
			goto('/', { replaceState: true });
		}
	});
</script>

{#if $page.error}
<ParaglideJS {i18n} languageTag={languageTag()}>
	<h1></h1>
</ParaglideJS>
{/if}