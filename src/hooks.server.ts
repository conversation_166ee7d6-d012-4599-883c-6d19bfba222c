import { i18n } from "$lib/i18n"
import { redirect, error } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';

// Hook do obsługi przekierowań
const redirectsHandle = async ({ event, resolve }) => {
	const url = event.url.pathname;

	// Przekierowania starych linków - obsługujemy zarówno z trailing slash jak i bez
	const redirects = {
		'/dla-placowki': '/dla-profesjonalistow/',
		'/dla-placowki/': '/dla-profesjonalistow/',
		'/dla-placowek': '/dla-profesjonalistow/',
		'/dla-placowek/': '/dla-profesjonalistow/',
		'/dla-placowki/produkty/wow': '/dla-profesjonalistow/produkty/wow/',
		'/dla-placowki/produkty/wow/': '/dla-profesjonalistow/produkty/wow/',
		'/dla-placowek/produkty/wow': '/dla-profesjonalistow/produkty/wow/',
		'/dla-placowek/produkty/wow/': '/dla-profesjonalistow/produkty/wow/',
		'/dla-placowki/produkty/syndose': '/dla-profesjonalistow/produkty/syndose/',
		'/dla-placowki/produkty/syndose/': '/dla-profesjonalistow/produkty/syndose/',
		'/dla-placowek/produkty/syndose': '/dla-profesjonalistow/produkty/syndose/',
		'/dla-placowek/produkty/syndose/': '/dla-profesjonalistow/produkty/syndose/',
		'/dla-placowki/produkty/wdm': '/dla-profesjonalistow/produkty/wdm/',
		'/dla-placowki/produkty/wdm/': '/dla-profesjonalistow/produkty/wdm/',
		'/dla-placowek/produkty/wdm': '/dla-profesjonalistow/produkty/wdm/',
		'/dla-placowek/produkty/wdm/': '/dla-profesjonalistow/produkty/wdm/',
		'/dla-lekarzy': '/dla-profesjonalistow/',
		'/dla-lekarzy/': '/dla-profesjonalistow/'
	};

	if (redirects[url]) {
		console.log(`Redirecting ${url} to ${redirects[url]}`);
		throw redirect(301, redirects[url]);
	}

	return await resolve(event);
};

export const handle = sequence(i18n.handle(), redirectsHandle)
