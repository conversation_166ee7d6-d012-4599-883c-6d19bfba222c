<!doctype html>
<html lang="%paraglide.lang%" dir="%paraglide.textDirection%">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@v3.0.0/dist/cookieconsent.css">
		<script defer src="https://umami.zbadani.pl/script.js" data-website-id="20af12a1-8e86-49cb-82c0-100b1e96c9c9"></script>
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div class="flex flex-col min-h-screen">%sveltekit.body%</div>
	</body>
	
	<script type="text/plain" data-category="analytics" data-service="Google Analytics" async
		src="https://www.googletagmanager.com/gtag/js?id=G-8TCPHQ0ZEZ">
	</script>

	<script type="text/plain" data-category="analytics" data-service="Google Analytics">
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());
		gtag('config', 'G-8TCPHQ0ZEZ');
	</script>

	<script type="text/plain" data-category="ads" data-service="LinkedIn Insight Tag">
		_linkedin_partner_id = "2032538";
		window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
		window._linkedin_data_partner_ids.push(_linkedin_partner_id);
	</script>

	<script type="text/plain" data-category="ads" data-service="LinkedIn Insight Tag">
		(function(l) {
		if (!l){window.lintrk = function(a,b){window.lintrk.q.push([a,b])};
		window.lintrk.q=[]}
		var s = document.getElementsByTagName("script")[0];
		var b = document.createElement("script");
		b.type = "text/javascript";b.async = true;
		b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
		s.parentNode.insertBefore(b, s);})(window.lintrk);
	</script>

	<script type="text/plain" data-category="ads" data-service="Meta (Facebook) Pixel">
		!function(f,b,e,v,n,t,s)
		{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
		n.callMethod.apply(n,arguments):n.queue.push(arguments)};
		if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
		n.queue=[];t=b.createElement(e);t.async=!0;
		t.src=v;s=b.getElementsByTagName(e)[0];
		s.parentNode.insertBefore(t,s)}(window, document,'script',
		'https://connect.facebook.net/en_US/fbevents.js');
		fbq('init', '1239401820802229');
		fbq('track', 'PageView');
	</script>

	<script type="module">
		import 'https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@v3.0.0/dist/cookieconsent.umd.js';

		/**
		 * All config. options available here:
		 * https://cookieconsent.orestbida.com/reference/configuration-reference.html
		 */
		CookieConsent.run({

			categories: {
				necessary: {
					enabled: true,  // this category is enabled by default
					readOnly: true  // this category cannot be disabled
				},
				analytics: {
					autoClear: {
						cookies: [
							{
								name: /^_ga/,   // regex: match all cookies starting with '_ga'
							},
							{
								name: '_gid',   // string: exact cookie name
							}
						]
					},
				},
				ads: {
					autoClear: {
						cookies: [
							{
								name: /^li/,
							},
							{
								name: /^_fb/, 
							},
							{
								name: /^fr/, 
							}
						]
					},
				},
			},

			language: {
				default: 'pl',
				autoDetect: 'document',
				translations: {
					pl: {
						consentModal: {
							title: 'Strona używa ciasteczek',
							description: 'Stosujemy pliki cookie w celu zapewnienia prawidłowego funkcjonowania serwisu. Możemy również wykorzystywać pliki cookie własne oraz naszych partnerów w celach analitycznych i marketingowych, w szczególności dopasowania treści reklamowych do Twoich preferencji. Korzystanie z analitycznych i marketingowych plików cookie wymaga zgody, którą możesz wyrazić, klikając „Akceptuj". Jeżeli chcesz dostosować swoje zgody dla nas i naszych partnerów, kliknij „Zarządzaj cookies". Wyrażoną zgodę możesz wycofać w każdym momencie, zmieniając wybrane ustawienia',
							acceptAllBtn: 'Akceptuj',
							acceptNecessaryBtn: 'Akceptuj tylko niezbędne',
							showPreferencesBtn: 'Zarządzaj cookies'
						},
						preferencesModal: {
							title: 'Zarządzaj ciasteczkami',
							acceptAllBtn: 'Zezwól na wszystkie',
							acceptNecessaryBtn: 'Odrzuć wszystkie',
							savePreferencesBtn: 'Akceptuj wybór',
							closeIconLabel: 'Zamknij',
							sections: [
								{
									title: 'Wymagane ciasteczka',
									description: 'Te pliki cookie są niezbędne do prawidłowego funkcjonowania witryny i nie można ich wyłączyć. Więcej informacji o korzystaniu przez nas i naszych partnerów z plików cookie oraz o przetwarzaniu Twoich danych osobowych, w tym o przysługujących Ci uprawnieniach, znajdziesz w naszej Polityce prywatności.',
									linkedCategory: 'necessary'
								},
								{
									title: 'Ciasteczka analityczne',
									description: 'Te pliki cookie zbierają informacje o tym, jak korzystasz z naszej witryny. Wszystkie dane są anonimizowane i nie mogą być użyte do zidentyfikowania Ciebie.',
									linkedCategory: 'analytics',
									cookieTable: {
										caption: 'Pliki cookie',
										headers: {
											name: 'Ciasteczko',
											domain: 'Domena',
											desc: 'Opis'
										},
										body: [
											{
												name: '_ga',
												domain: 'zbadani.pl',
												desc: 'Służy do rozróżniania unikalnych użytkowników poprzez przypisanie losowo wygenerowanego numeru jako identyfikatora klienta.'
											},
											{
												name: '_gid',
												domain: 'zbadani.pl',
												desc: 'Służy do przechowywania i aktualizacji wartości unikalnych dla każdej odwiedzanej strony.'
											}
										]
									}
								},
								{
									title: 'Ciasteczka marketingowe',
									description: 'Te pliki cookie służą do tworzenia bardziej trafnych komunikatów reklamowych dla Ciebie i Twoich zainteresowań. Celem jest wyświetlanie reklam, które są trafne i angażujące dla poszczególnych użytkowników, a tym samym bardziej wartościowe dla wydawców i zewnętrznych reklamodawców.',
									linkedCategory: 'ads',
									cookieTable: {
										caption: 'Pliki cookie',
										headers: {
											name: 'Ciasteczko',
											domain: 'Domena',
											desc: 'Opis'
										},
										body: [
											{
												name: 'li_oatml',
												domain: 'linkedin.com',
												desc: 'Używane przez LinkedIn do identyfikacji członków poza LinkedIn w kontekście reklam w oparciu o zachowania na stronach trzecich.'
											},
											{
												name: 'li_fat_id',
												domain: 'linkedin.com',
												desc: 'Służy do śledzenia aktywności użytkowników na stronie internetowej poprzez wbudowane funkcje LinkedIn.'
											},
											{
												name: '_fbp',
												domain: 'facebook.com',
												desc: 'Służy do dostarczania serii produktów reklamowych, takich jak licytowanie w czasie rzeczywistym od reklamodawców zewnętrznych.'
											},
											{
												name: 'fr',
												domain: 'facebook.com',
												desc: 'Służy do dostarczania, pomiaru i ulepszania trafności reklam.'
											}
										]
									}
								}
							]
						}
					},
					en: {
						consentModal: {
							title: 'This website uses cookies',
							description: 'We use cookies to ensure the proper functioning of the website. We may also use our own and our partners\' cookies for analytical and marketing purposes, in particular to tailor advertising content to your preferences. The use of analytical and marketing cookies requires consent, which you can give by clicking \'Accept\'. If you want to customize your consents for us and our partners, click \'Manage cookies\'. You can withdraw your consent at any time by changing the selected settings',
							acceptAllBtn: 'Accept',
							acceptNecessaryBtn: 'Accept only necessary',
							showPreferencesBtn: 'Manage cookies'
						},
						preferencesModal: {
							title: 'Manage cookies',
							acceptAllBtn: 'Allow all',
							acceptNecessaryBtn: 'Reject all',
							savePreferencesBtn: 'Accept selection',
							closeIconLabel: 'Close',
							sections: [
								{
									title: 'Required cookies',
									description: 'These cookies are necessary for the proper functioning of the website and cannot be disabled. For more information about how we and our partners use cookies and process your personal data, including your rights, please see our Privacy Policy.',
									linkedCategory: 'necessary'
								},
								{
									title: 'Analytical cookies',
									description: 'These cookies collect information about how you use our website. All data is anonymized and cannot be used to identify you.',
									linkedCategory: 'analytics',
									cookieTable: {
										caption: 'Cookies',
										headers: {
											name: 'Cookie',
											domain: 'Domain',
											desc: 'Description'
										},
										body: [
											{
												name: '_ga',
												domain: 'zbadani.pl',
												desc: 'Used to distinguish unique users by assigning a randomly generated number as a client identifier.'
											},
											{
												name: '_gid',
												domain: 'zbadani.pl',
												desc: 'Used to store and update unique values for each visited page.'
											}
										]
									}
								},
								{
									title: 'Marketing cookies',
									description: 'These cookies are used to create more relevant advertising messages for you and your interests. The goal is to display ads that are relevant and engaging for individual users, and thus more valuable for publishers and external advertisers.',
									linkedCategory: 'ads',
									cookieTable: {
										caption: 'Cookies',
										headers: {
											name: 'Cookie',
											domain: 'Domain',
											desc: 'Description'
										},
										body: [
											{
												name: 'li_oatml',
												domain: 'linkedin.com',
												desc: 'Used by LinkedIn to identify members outside of LinkedIn in the context of behavior-based advertising on third-party sites.'
											},
											{
												name: 'li_fat_id',
												domain: 'linkedin.com',
												desc: 'Used to track user activity on the website through embedded LinkedIn features.'
											},
											{
												name: '_fbp',
												domain: 'facebook.com',
												desc: 'Used to deliver a series of advertisement products such as real-time bidding from third-party advertisers.'
											},
											{
												name: 'fr',
												domain: 'facebook.com',
												desc: 'Used to deliver, measure and improve ad relevancy.'
											}
										]
									}
								}
							]
						}
					},
					uk: {
						consentModal: {
							title: 'Цей сайт використовує файли cookie',
							description: 'Ми використовуємо файли cookie для забезпечення належного функціонування веб-сайту. Ми також можемо використовувати власні файли cookie та файли cookie наших партнерів для аналітичних та маркетингових цілей, зокрема для адаптації рекламного вмісту до ваших уподобань. Використання аналітичних та маркетингових файлів cookie вимагає згоди, яку ви можете надати, натиснувши \'Прийняти\'. Якщо ви хочете налаштувати свої згоди для нас та наших партнерів, натисніть \'Керувати файлами cookie\'. Ви можете відкликати свою згоду в будь-який момент, змінивши вибрані налаштування',
							acceptAllBtn: 'Прийняти',
							acceptNecessaryBtn: 'Прийняти лише необхідні',
							showPreferencesBtn: 'Керувати файлами cookie'
						},
						preferencesModal: {
							title: 'Керування файлами cookie',
							acceptAllBtn: 'Дозволити всі',
							acceptNecessaryBtn: 'Відхилити всі',
							savePreferencesBtn: 'Прийняти вибір',
							closeIconLabel: 'Закрити',
							sections: [
								{
									title: 'Обов\'язкові файли cookie',
									description: 'Ці файли cookie необхідні для належного функціонування веб-сайту і не можуть бути вимкнені. Для отримання додаткової інформації про те, як ми та наші партнери використовуємо файли cookie та обробляємо ваші особисті дані, включаючи ваші права, ознайомтеся з нашою Політикою конфіденційності.',
									linkedCategory: 'necessary'
								},
								{
									title: 'Аналітичні файли cookie',
									description: 'Ці файли cookie збирають інформацію про те, як ви використовуєте наш веб-сайт. Усі дані анонімізовані і не можуть бути використані для вашої ідентифікації.',
									linkedCategory: 'analytics',
									cookieTable: {
										caption: 'Файли cookie',
										headers: {
											name: 'Cookie',
											domain: 'Домен',
											desc: 'Опис'
										},
										body: [
											{
												name: '_ga',
												domain: 'zbadani.pl',
												desc: 'Використовується для розрізнення унікальних користувачів шляхом присвоєння випадково згенерованого номера як ідентифікатора клієнта.'
											},
											{
												name: '_gid',
												domain: 'zbadani.pl',
												desc: 'Використовується для зберігання та оновлення унікальних значень для кожної відвіданої сторінки.'
											}
										]
									}
								},
								{
									title: 'Маркетингові файли cookie',
									description: 'Ці файли cookie використовуються для створення більш релевантних рекламних повідомлень для вас та ваших інтересів. Метою є відображення реклами, яка є релевантною та привабливою для окремих користувачів, і, таким чином, більш цінною для видавців та зовнішніх рекламодавців.',
									linkedCategory: 'ads',
									cookieTable: {
										caption: 'Файли cookie',
										headers: {
											name: 'Cookie',
											domain: 'Домен',
											desc: 'Опис'
										},
										body: [
											{
												name: 'li_oatml',
												domain: 'linkedin.com',
												desc: 'Використовується LinkedIn для ідентифікації членів поза LinkedIn у контексті реклами на основі поведінки на сторонніх сайтах.'
											},
											{
												name: 'li_fat_id',
												domain: 'linkedin.com',
												desc: 'Використовується для відстеження активності користувачів на веб-сайті через вбудовані функції LinkedIn.'
											},
											{
												name: '_fbp',
												domain: 'facebook.com',
												desc: 'Використовується для доставки серії рекламних продуктів, таких як торги в реальному часі від сторонніх рекламодавців.'
											},
											{
												name: 'fr',
												domain: 'facebook.com',
												desc: 'Використовується для доставки, вимірювання та покращення релевантності реклами.'
											}
										]
									}
								}
							]
						}
					}
				}
			}
		});
	</script>
	<style>
		#cc-main {
			font-family: 'Lexend', 'Rubik';
		}
		#cc-main .cm__btn {
			background: #002AA3;
		}
		#cc-main .cm__btn--secondary {
			background: #E6ECFF;
		}
	</style>
</html>
