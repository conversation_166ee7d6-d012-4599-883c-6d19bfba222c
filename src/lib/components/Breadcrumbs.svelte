<script lang="ts">
  import { goto } from '$app/navigation';
	import BackImg from '$lib/assets/images/back.svg'
  export let current; // Current page name
  export let previous; // Previous page name
  export let previousUrl:any; // URL to navigate to when clicking "previous"

  function navigateToPrevious() {
    if (previousUrl) {
      goto(previousUrl);
    }
  }
</script>

<nav class="breadcrumbs">
  {#if previous && previousUrl}
    <button class="flex items-center gap-2" on:click={navigateToPrevious}>
      <img src={BackImg} alt="Powrót"> <span class="underline">{previous}</span>
    </button>
    <span>&nbsp;&rsaquo;&nbsp;</span>
  {/if}
  <span>{current}</span>
</nav>

<style type="postcss">
  .breadcrumbs {
    display: flex;
    align-items: center;
    font-size: 0.8125rem;
    color: #4D4D4D;
    @apply mb-5;
  }
  @media screen and (max-width: 640px) {
    .breadcrumbs {
			@apply hidden;
		}
  }
</style>
