<script>
	import * as m from '$lib/paraglide/messages';
	import <PERSON><PERSON><PERSON> from '$lib/assets/images/team-ma.webp';
	import TeamZ<PERSON> from '$lib/assets/images/team-zu.webp';

	export let product = 'syndose'; // syndose, wow, wdm

	function showContactFirst() {
		var scfEl = document.getElementById("showContactFirst");
		var scfBtn = document.getElementById("showContactFirstButton");
		if(scfEl) {
			scfEl.style.display = "block";
			if(scfBtn) {
				scfBtn.remove();
			}
			scfEl.innerHTML='<a class="btn" href="mailto:<EMAIL>"><EMAIL></a>';
		}
	}

	// Get product-specific content
	// @ts-ignore
	function getContactContent(product) {
		switch(product) {
			case 'wow':
				return {
					p1: m.contact_wow_p1(),
					p2: m.contact_wow_p2()
				};
			case 'wdm':
				return {
					p1: m.contact_wdm_p1(),
					p2: m.contact_wdm_p2()
				};
			default: // syndose
				return {
					p1: m.contact_syndose_p1(),
					p2: m.contact_syndose_p2()
				};
		}
	}

	$: content = getContactContent(product);
</script>

<section id="skontaktuj-sie-z-nami" class="bg-blue-900">
	<div class="md:flex container mx-auto">

		<div class="md:w-1/2">
			<h2 class="mb-4">{m.contact_section_title()}</h2>
			<div class="pr-12">
				<p>{content.p1}</p>
				<p>{content.p2}</p>
			</div>
		</div>

		<div class="md:w-1/2 md:flex bg-white p-12 rounded-2xl gap-12 align-middle justify-center items-center">
			<div class="md:w-1/2">
				<h2 class="prehead mb-4">{@html m.contact_section_sales_title()}</h2>
				<button id="showContactFirstButton" class="btn mb-6 md:mb-0" on:click={showContactFirst}>{m.contact_section_show_contact()}</button>
				<div id="showContactFirst" class="hidden mb-6 md:mb-0">test</div>
			</div>
			<div class="md:w-1/2">
				<div class="flex">
					<div class="md:ml-auto relative w-32 h-auto">
						<img class="min-w-12 min-h-12 rounded-full border-2 border-white z-30" src="{TeamMa}" alt="Marta">
					</div>
					<div class="-ml-8 relative w-32 h-auto">
						<img class="min-w-12 min-h-12 rounded-full border-2 border-white z-10" src="{TeamZu}" alt="Zuzanna">
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<style lang="postcss">
	section ul {
		@apply text-blue-300;
	}
	section p {
		@apply text-blue-300 mb-4;
	}
	section h2 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h2.prehead {
		font-size: 1.3125rem;
		@apply text-blue-300;
	}
	section h3 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h4 {
		font-size: 1.3125rem;
		@apply text-blue-300 font-normal;
	}
	section {
		@apply py-32;
	}

	@media screen and (max-width: 1024px) {
		section {
			@apply py-8;
		}
		h3 br, h4 br {
			@apply hidden;
		}

		.products > div {
			@apply border p-8 rounded-2xl mb-6;
			border: 1px solid #BECDF8;
		}
	}
</style>