<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
  	import { PUBLIC_DIRECTUS_URL } from "$env/static/public";
	export let posts: any;
	
	function formatDate(dateString:any) {
		const date = new Date(dateString);
		const day = date.getDate();
		const month = date.getMonth() + 1; // January is 0, so we add 1 to get the correct month
		const year = date.getFullYear();
		const formattedDay = day < 10 ? '0' + day : day;
		const formattedMonth = month < 10 ? '0' + month : month;
		return formattedDay + '.' + formattedMonth + '.' + year;
	}

</script>

{#if posts && posts.length > 0}
	{#each posts as post, index (post.id)}
		<div class="card mx-auto max-w-[900px]">

			{#if post.cover}
				<div class="flex flex-col justify-center align-center items-center relative overflow-hidden min-h-96 bg-blue-500 rounded-xl mb-8">
					<div class="top-0 z-50 left-0 w-full absolute opacity-50 min-h-96 bg-cover" style="background: url('{PUBLIC_DIRECTUS_URL}/assets/{post.cover} ');"></div>
					<h1 class="relative z-[999] mt-0 text-white text-center px-4 font-normal mb-1">{formatDate(post.date_created)}</h1>
					<h1 class="relative z-[999] mt-1 text-white text-4xl text-center px-4 font-medium mb-4">{post.title}</h1>
				</div>
	
			{/if}
			<div>
				<div class="text-blue-300 text-normal">{@html post.content}</div>
				<a href="/knowledge" class="text-blue-300 underline mt-8 mb-8 inline-block">&larr; {m.back_to_article_list()}</a>
			</div>
		</div>
	{/each}
{:else}
	<p>{m.articles_not_found}</p>
{/if}

<style lang="postcss">
	:global(.card h2), :global(.card h2 strong), :global(.card p strong)  {
		font-weight: 600;
	}
	:global(.card p) {
		@apply leading-5 mb-4;
	}
</style>