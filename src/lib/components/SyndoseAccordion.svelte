<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import clsx, { type ClassValue } from 'clsx'
	import { twMerge } from 'tailwind-merge';

	import { createAccordion, melt } from '@melt-ui/svelte';
	import { slide } from 'svelte/transition';

	// @ts-ignore
    import SolarAltArrowDownOutline from '~icons/solar/alt-arrow-down-outline'
	// @ts-ignore
    import SolarAltArrowUpOutline from '~icons/solar/alt-arrow-up-outline'

	function cn(...classes: ClassValue[]) {
		return twMerge(clsx(classes))
	}

	let selectedAccordionItem: any = 'item-1';

	const {
		elements: { content, item, trigger, root },
		helpers: { isSelected },
	} = createAccordion({
		defaultValue: 'item-1',
		onValueChange: ({ curr, next }) => {
			selectedAccordionItem = next;
			return next
		},
	});

    interface Item {
        id: string;
        title: string;
        description: string;
    }

	const items: Item[] = [
		{
			id: 'item-1',
			title: m.syndose_faq_1_q(),
			description: m.syndose_faq_1_a()
		},
		{
			id: 'item-2',
			title: m.syndose_faq_2_q(),
			description: m.syndose_faq_2_a()
		},
		{
			id: 'item-3',
			title: m.syndose_faq_3_q(),
			description: m.syndose_faq_3_a()
		},
		{
			id: 'item-4',
			title: m.syndose_faq_4_q(),
			description: m.syndose_faq_4_a()
		},
		{
			id: 'item-5',
			title: m.syndose_faq_5_q(),
			description: m.syndose_faq_5_a()
		}
	];

	let className = '';
	export { className as class };
</script>

<div class="">
	<div class="flex items-center w-full">
		<div
		class={cn(
			'my-16',
			className,
		)}
		{...$root}
		>
		{#each items as { id, title, description }, i}
			<div
			use:melt={$item(id)}
			class="mb-4"
			>
			<h2 class="flex">
				<button
				use:melt={$trigger(id)}
				class={cn(
					'flex flex-1 cursor-pointer items-center',
					'border-2 border-blue-900 rounded-xl hover:border-blue-500 hover:text-blue-500',
					'px-6 pt-6 pb-6',
                    'text-blue-300 font-semibold underline',
                    $isSelected(id) && 'hover:border-transparent no-underline bg-blue-900 rounded-t-xl rounded-b-none'
				)}
				>
				{title}
                {#if $isSelected(id)}<SolarAltArrowUpOutline class="ml-auto text-2xl" />{:else}<SolarAltArrowDownOutline class="text-2xl ml-auto opacity-25" />{/if}
				</button>
			</h2>
			{#if $isSelected(id)}
				<div
				class={cn(
					'text-blue-300 bg-blue-900 rounded-b-xl',
				)}
				use:melt={$content(id)}
				transition:slide
				>
					<div class="px-6 pb-6 text-blue-100 text-left">
						{description}
					</div>
				</div>
			{/if}
			</div>
		{/each}
		</div>

	</div>
</div>