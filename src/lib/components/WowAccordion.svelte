<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import clsx, { type ClassValue } from 'clsx'
	import { twMerge } from 'tailwind-merge';

	import { createAccordion, melt } from '@melt-ui/svelte';
	import { slide } from 'svelte/transition';

	// @ts-ignore
    import SolarAltArrowDownOutline from '~icons/solar/alt-arrow-down-outline'
	// @ts-ignore
    import SolarAltArrowUpOutline from '~icons/solar/alt-arrow-up-outline'

	function cn(...classes: ClassValue[]) {
		return twMerge(clsx(classes))
	}

	let selectedAccordionItem: any = 'item-1';

	const {
		elements: { content, item, trigger, root },
		helpers: { isSelected },
	} = createAccordion({
		defaultValue: 'item-1',
		onValueChange: ({ curr, next }) => {
			selectedAccordionItem = next;
			return next
		},
	});

    interface Item {
        id: string;
        title: string;
        description: string;
    }

	const items: Item[] = [
		{
			id: 'item-1',
			title: m.wow_faq_1_q(),
			description: m.wow_faq_1_a()
		},
		{
			id: 'item-2',
			title: m.wow_faq_2_q(),
			description: m.wow_faq_2_a()
		},
		{
			id: 'item-3',
			title: m.wow_faq_3_q(),
			description: m.wow_faq_3_a()
		},
		{
			id: 'item-4',
			title: m.wow_faq_4_q(),
			description: m.wow_faq_4_a()
		},
		{
			id: 'item-5',
			title: m.wow_faq_5_q(),
			description: m.wow_faq_5_a()
		},
	];

	let className = '';
	export { className as class };
</script>

<div>
	<div>
		<div
		class={cn(
			className,
		)}
		{...$root}
		>
		{#each items as { id, title, description }, i}
			<div
			use:melt={$item(id)}
			class="mb-4"
			>
			<h2 class="flex">
				<button
				use:melt={$trigger(id)}
				class={cn(
					'flex flex-1 cursor-pointer',
					'border border-[#BECDF8] rounded-xl hover:border-blue-500 hover:text-blue-500',
					'px-6 pt-6 pb-6',
                    'text-blue-300 bg-white text-left',
                    $isSelected(id) && 'no-underline rounded-b-none border-b-0 hover:border-[#BECDF8]'
				)}
				>
				{title}
                {#if $isSelected(id)}<SolarAltArrowUpOutline class="ml-auto text-2xl" />{:else}<SolarAltArrowDownOutline class="text-2xl ml-auto opacity-25" />{/if}
				</button>
			</h2>
			{#if $isSelected(id)}
				<div
				class={cn(
					'text-[#666] bg-blue-900',
				)}
				use:melt={$content(id)}
				transition:slide
				>
					<div class="px-6 pb-6 text-left bg-white rounded-b-xl border border-[#BECDF8] border-t-0 text-blue-100">
						{@html description}
					</div>
				</div>
			{/if}
			</div>
		{/each}
		</div>

	</div>
</div>