<script lang="ts">
    import * as m from "$lib/paraglide/messages.js";
    import { languageTag } from "$lib/paraglide/runtime";
    import { browser } from "$app/environment";
    export let show:any;

    const agreeToProfessional = () => {
        if (browser) {
            localStorage.setItem('professionalAgreed', 'true');
            show = false;
            //window.location.reload();
        }
    };

    const notProfessional = () => {
        if (browser) {
            window.location.href = '/';
        }
    };
</script>

{#if show}
    <div class="popupContainer fixed w-screen h-screen flex flex-col items-center content-center justify-center z-50">
        <div class="popup p-8 rounded-2xl bg-white max-w-xl shadow-2xl">
            <p class="mb-4">{m.pro_popup()}</p>
            <button class="btn mr-4" on:click={agreeToProfessional}>{m.yes()}</button>
            <button class="btn btn-destructive" on:click={notProfessional}>{m.no()}</button>
        </div>
    </div>
{/if}

<style lang="postcss">
    .popupContainer {
        background: rgba(233, 242, 251, 0.5);
    }
</style>