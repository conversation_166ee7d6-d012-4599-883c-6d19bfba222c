<script lang="ts">
  // @ts-nocheck
  import * as m from "$lib/paraglide/messages.js";

  // @ts-ignore
  import SolarLogin3Outline from "~icons/solar/login-3-outline";
  // @ts-ignore
  import SolarHealthOutline from "~icons/solar/health-outline";
  // @ts-ignore
  import SolarDocumentTextOutline from "~icons/solar/document-text-outline";
  // @ts-ignore
  import SolarLinkOutline from "~icons/solar/link-outline";
  // @ts-ignore
  import SolarCloseCircleOutline from "~icons/solar/close-circle-outline";

  import IconDialogHospital from "$lib/assets/images/dialog_hospital.svg";
  import IconDialogPIN from "$lib/assets/images/dialog_pin.svg";

  export let show: boolean = false;

  const closeModal = () => {
    show = false;
  };

  const handleBackdropClick = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  };

  const loginOptions = [
    {
      title: m.login_modal_wdm_title(),
      description: m.login_modal_wdm_desc(),
      url: "https://wdm.zbadani.pl",
      icon: IconDialogHospital
    },
    {
      title: m.login_modal_link_title(),
      description: m.login_modal_link_desc(),
      url: "https://link.zbadani.pl",
      icon: IconDialogPIN
    }
  ];
</script>

{#if show}
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 bg-blue-900"
    on:click={handleBackdropClick}
    on:keydown={(e) => e.key === 'Escape' && closeModal()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
  >
    <div class="bg-white rounded-2xl shadow-2xl w-full sm:min-w-[480px] max-w-[720px] max-h-[90vh] overflow-y-auto border-width-[5px] border-[#eee]">
      <!-- Header -->
      <div class="flex items-center justify-between p-6">
        <div class="sm:block hidden">
            <h2 id="modal-title" class="text-2xl font-medium text-blue-300">
                {m.exam_modal_title()}
              </h2>
            <p>{m.exam_modal_desc()}</p>
        </div>
        <button
          on:click={closeModal}
          class="w-12 h-12 bg-blue-900 rounded-full flex items-center justify-center text-blue-300 hover:text-blue-500 transition-colors"
          aria-label="Zamknij"
        >
          <SolarCloseCircleOutline class="text-3xl" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 pt-0">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-0">
          {#each loginOptions as option, index}
            <a
              href={option.url}
              target="_blank"
            class="flex align-items justify-center items-center p-4 bg-white hover:bg-[rgba(238,242,252,0.5)] border border-blue-800 text-center {index === 0 ? 'sm:rounded-l-xl sm:border-r-0 sm:rounded-r-none rounded-xl' : ''} {index === loginOptions.length - 1 ? 'sm:rounded-r-xl sm:rounded-l-none rounded-xl' : ''}"
              on:click={closeModal}
            >
              <div class="flex flex-col items-center space-y-4">
                <div>
                  <img src="{option.icon}" alt="Icon" class="mx-auto text-center mb-6 mt-2" />
                  <h3 class="font-base text-center text-blue-500 font-medium text-base">
                    {option.title}
                  </h3>
                  <p class="text-base">
                    {option.description}
                  </p>
                </div>
              </div>
            </a>
          {/each}
        </div>
      </div>
    </div>
  </div>
{/if}

<style lang="postcss">
  /* Responsive adjustments for mobile */
  @media (max-width: 768px) {
    .grid {
      @apply grid-cols-1 gap-3;
    }

    .w-16.h-16 {
      @apply w-12 h-12;
    }
  }
</style>
