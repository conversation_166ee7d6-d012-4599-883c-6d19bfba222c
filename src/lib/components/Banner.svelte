<script lang="ts">
  import { animate, inView, stagger } from "motion"
	import { onMount } from "svelte";

	onMount(async () => {
			inView(".card", ({ target }) => {
				animate(
					target,
					{ transform: "translateY(0px)", opacity: 1 },
					{ duration: 1, easing: [0.17, 0.55, 0.55, 1] },
				);
			})
	});

  export let src: string;
</script>  
  
<div class="lg:min-h-[calc(100vh-145px)] bg-cover bg-no-repeat bg-center lg:py-0 py-4 flex items-center cclass bg-blue-900" style="background-image: url({src})">
  <div class="container mx-auto">
    <div class="card opacity-0 translate-y-4 p-8 rounded-2xl bg-white w-fit lg:max-w-128 w-full">
      <slot name="bannerContent"></slot>
    </div>
  </div>
</div>

<style lang="postcss">
	@media screen and (max-width: 640px) {
		.cclass {
			@apply bg-none !important;
		}
		div.bg-cover {
			@apply pt-8
		}
	}
</style>