<script>
	import * as m from '$lib/paraglide/messages';
</script>

<section id="proces" class="bg-white">
	<h2 class="text-center prehead">{m.syndose_process_title()}</h2>
	<h3 class="text-center">{m.syndose_process_subtitle()}</h3>
	<div class="md:flex gap-8 container mx-auto pt-8 text-center">
		<div class="w-full">
			<span class="w-16 h-16 rounded-full bg-blue-900 text-2xl leading-none flex items-center mx-auto mb-4 text-blue-500">
				<h5 class="text-center w-full relative -top-[2px]">1</h5>
			</span>
			<h4 class="text-blue-300 mb-2">{m.syndose_process_1_title()}</h4>
			<p>{m.syndose_process_1_desc()}</p>
		</div>

		<div class="w-full">
			<span class="w-16 h-16 rounded-full bg-blue-900 text-2xl leading-none flex items-center mx-auto mb-4 text-blue-500">
				<h5 class="text-center w-full relative -top-[2px]">2</h5>
			</span>
			<h4 class="text-blue-300 mb-2">{m.syndose_process_2_title()}</h4>
			<p>{m.syndose_process_2_desc()}</p>
		</div>

		<div class="w-full">
			<span class="w-16 h-16 rounded-full bg-blue-900 text-2xl leading-none flex items-center mx-auto mb-4 text-blue-500">
				<h5 class="text-center w-full relative -top-[2px]">3</h5>
			</span>
			<h4 class="text-blue-300 mb-2">{m.syndose_process_3_title()}</h4>
			<p>{m.syndose_process_3_desc()}</p>
		</div>

		<div class="w-full">
			<span class="w-16 h-16 rounded-full bg-blue-900 text-2xl leading-none flex items-center mx-auto mb-4 text-blue-500">
				<h5 class="text-center w-full relative -top-[2px]">4</h5>
			</span>
			<h4 class="text-blue-300 mb-2">{m.syndose_process_4_title()}</h4>
			<p>{m.syndose_process_4_desc()}</p>
		</div>
	</div>
</section>

<style lang="postcss">
	section ul {
		@apply text-blue-300;
	}
	section p {
		@apply text-blue-300 mb-4;
	}
	section h2 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h2.prehead {
		font-size: 1.3125rem;
		@apply text-blue-300;
	}
	section h3 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h4 {
		font-size: 1.3125rem;
		@apply text-blue-300 font-normal;
	}
	section {
		@apply py-32;
	}

	@media screen and (max-width: 1024px) {
		section {
			@apply py-8;
		}
		h3 br, h4 br {
			@apply hidden;
		}

		.products > div {
			@apply border p-8 rounded-2xl mb-6;
			border: 1px solid #BECDF8;
		}
	}
</style>
