<script>
	import * as m from '$lib/paraglide/messages';
	import LogoSyndose from '$lib/assets/images/logo_syndose.svg';
	import LogoWOW from '$lib/assets/images/logo_wow_new.svg';
	import LogoWDM from '$lib/assets/images/logo_wdm_new.svg';
	import LogoAI from '$lib/assets/images/logo_ai_new.svg';
	import LogoPP from '$lib/assets/images/logo_pp_new.svg';

	export let currentProduct = '';
</script>

<section id="zobacz-tez" class="bg-white">
	<h2 class="prehead text-center">{m.see_also_title()}</h2>
	<h3 class="text-center mb-16">{m.see_also_subtitle()}</h3>
	<div class="md:flex container mx-auto gap-12 products">
		{#if currentProduct !== 'syndose'}
			<div class="w-full flex flex-col items-center text-center gap-4">
				<img class="max-w-16 max-h-16" src="{LogoSyndose}" alt="SynDose">
				<h4>SynDose</h4>
				<p>{m.syndose_feature_reports_desc()}</p>
				<a href="/professionals/products/syndose" class="btn">{m.learn_more()}</a>
			</div>
		{/if}
		
		{#if currentProduct !== 'wow'}
			<div class="w-full flex flex-col items-center text-center gap-4">
				<img src="{LogoWOW}" alt="WOW">
				<h4>{m.wow_title()}</h4>
				<p>{m.wow_desc()}</p>
				<a href="/professionals/products/wow" class="btn">{m.learn_more()}</a>
			</div>
		{/if}
		
		{#if currentProduct !== 'wdm'}
			<div class="w-full flex flex-col items-center text-center gap-4">
				<img src="{LogoWDM}" alt="WDM">
				<h4>{m.wdm_title()}</h4>
				<p>{m.wdm_desc()}</p>
				<a href="/professionals/products/wdm" class="btn">{m.learn_more()}</a>
			</div>
		{/if}
		
		<div class="w-full flex flex-col items-center text-center gap-4">
			<img src="{LogoAI}" alt="AI">
			<h4>{m.ai_title()}</h4>
			<p>{m.ai_desc()}</p>
		</div>
		
		<div class="w-full flex flex-col items-center text-center gap-4">
			<img src="{LogoPP}" alt="Portal Pacjenta">
			<h4>{m.portal_title()}</h4>
			<p>{m.portal_desc()}</p>
		</div>
	</div>
</section>

<style lang="postcss">
	section ul {
		@apply text-blue-300;
	}
	section p {
		@apply text-blue-300 mb-4;
	}
	section h2 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h2.prehead {
		font-size: 1.3125rem;
		@apply text-blue-300;
	}
	section h3 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h4 {
		font-size: 1.3125rem;
		@apply text-blue-300 font-normal;
	}
	section {
		@apply py-32;
	}

	@media screen and (max-width: 1024px) {
		section {
			@apply py-8;
		}
		h3 br, h4 br {
			@apply hidden;
		}

		.products > div {
			@apply border p-8 rounded-2xl mb-6;
			border: 1px solid #BECDF8;
		}
	}
</style>