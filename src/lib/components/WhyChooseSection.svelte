<script>
	import * as m from '$lib/paraglide/messages';
	import LogoSynektik from '$lib/assets/images/logo-synektik-blue.svg';
</script>

<section id="dlaczego-warto" class="bg-white">
	<div class="md:flex mx-auto container items-center">
		<div class="md:w-1/2 hidden md:block">
			<img src="{LogoSynektik}" class="h-auto w-full max-w-96 mx-auto" alt="Logo Synektik.com.pl">
		</div>
		<div class="md:w-1/2">
			<h2 class="prehead">{m.syndose_why_choose_title()}</h2>
			<h3 class="mb-4">{m.syndose_why_choose_subtitle()}</h3>
			<p>{m.syndose_why_choose_p1()}</p>
			<p>{m.syndose_why_choose_p2()}</p>
			<ul class="pl-4 list-disc -mt-2">
				<li>{m.syndose_why_choose_1()}</li>
				<li>{m.syndose_why_choose_2()}</li>
				<li>{m.syndose_why_choose_3()}</li>
				<li>{m.syndose_why_choose_4()}</li>
				<li>{m.syndose_why_choose_5()}</li>
				<li>{m.syndose_why_choose_6()}</li>
			</ul>
		</div>
	</div>
</section>

<style lang="postcss">
	section ul {
		@apply text-blue-300;
	}
	section p {
		@apply text-blue-300 mb-4;
	}
	section h2 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h2.prehead {
		font-size: 1.3125rem;
		@apply text-blue-300;
	}
	section h3 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h4 {
		font-size: 1.3125rem;
		@apply text-blue-300 font-normal;
	}
	section {
		@apply py-32;
	}

	@media screen and (max-width: 1024px) {
		section {
			@apply py-8;
		}
		h3 br, h4 br {
			@apply hidden;
		}

		.products > div {
			@apply border p-8 rounded-2xl mb-6;
			border: 1px solid #BECDF8;
		}
	}
</style>