<script lang="ts">
	export let showA11yWidget;
	import * as m from '$lib/paraglide/messages.js';
	//@ts-ignore
	import SolarAddCircleOutline from '~icons/solar/add-circle-outline'
	//@ts-ignore
	import SolarTextCircleOutline from '~icons/solar/text-circle-outline'
	//@ts-ignore
	import SolarMoonOutline from '~icons/solar/moon-outline'
	//@ts-ignore
	import SolarTextUnderlineCircleOutline from '~icons/solar/text-underline-circle-outline'
	//@ts-ignore
	import SolarRunningOutline from '~icons/solar/running-outline'

	// Initialize state with localStorage values or defaults
	let fontSize = localStorage.getItem('fontSize') === 'true';
	let dyslexia = localStorage.getItem('dyslexia') === 'true';
	let contrast = localStorage.getItem('contrast') === 'true';
	let links = localStorage.getItem('links') === 'true';
	let motion = localStorage.getItem('motion') === 'true';

	// Functions to toggle settings and update localStorage
	function toggleFontSize() {
		fontSize = !fontSize;fontSize ? document.documentElement.classList.add('a11yFontSize') : document.documentElement.classList.remove('a11yFontSize');
		updateLocalStorage('fontSize', fontSize.toString());
	}

	function toggleDyslexia() {
		dyslexia = !dyslexia;
		dyslexia ? document.documentElement.classList.add('a11yDyslexia') : document.documentElement.classList.remove('a11yDyslexia');
		updateLocalStorage('dyslexia', dyslexia.toString());
	}

	function toggleContrast() {
		contrast = !contrast;
		contrast ? document.documentElement.classList.add('a11yContrast') : document.documentElement.classList.remove('a11yContrast');
		updateLocalStorage('contrast', contrast.toString());
	}

	function toggleLinks() {
		links = !links;
		links ? document.documentElement.classList.add('a11yLinks') 	  : document.documentElement.classList.remove('a11yLinks');
		updateLocalStorage('links', links.toString());
	}

	function toggleMotion() {
		motion = !motion;
		motion ? document.documentElement.classList.add('a11yMotion') 	  : document.documentElement.classList.remove('a11yMotion');
		updateLocalStorage('motion', motion.toString());
	}

	function updateLocalStorage(key: any, value: any) {
		localStorage.setItem(key, value);
	}
</script>

<div
	id="a11yWidget"
	class="fixed top-12 right-0 m-4 p-4 bg-blue-900 rounded-xl"
	style="z-index: 1000;"
>
	<div class="grid grid-cols-2 gap-4">
		<button on:click={toggleFontSize} class:active={fontSize}>
			<SolarAddCircleOutline class="text-2xl mx-auto mb-2" />
			{m.a11y_increase()} font
		</button>

		<button on:click={toggleDyslexia} class:active={dyslexia}>
			<SolarTextCircleOutline class="text-2xl mx-auto mb-2" />
			{m.a11y_dyslexia()}
		</button>

		<button on:click={toggleContrast} class:active={contrast}>
			<SolarMoonOutline class="text-2xl mx-auto mb-2" />
			{m.a11y_inverseColors()}
		</button>

		<button on:click={toggleLinks} class:active={links}>
			<SolarTextUnderlineCircleOutline class="text-2xl mx-auto mb-2" />
			{m.a11y_highlightHrefs()}
		</button>

		<button on:click={toggleMotion} class:active={motion}>
			<SolarRunningOutline class="text-2xl mx-auto mb-2" />
			{m.a11y_reduceMotion()}
		</button>
	</div>

	<div>
		<button class="w-full mt-4" on:click={() => (showA11yWidget = false)}>
			{m.close()}
		</button>
	</div>
</div>

<style lang="postcss">
	button:hover {
		@apply border-blue-500;
	}
	button {
		@apply text-center rounded-lg p-4 bg-white inline-block text-blue-500 border-white border-2;
	}

	button.active {
		@apply bg-blue-500 text-white;
	}
</style>
