<script lang="ts">
  // @ts-nocheck
  import * as m from "$lib/paraglide/messages.js";

  // @ts-ignore
  import SolarLogin3Outline from "~icons/solar/login-3-outline";
  // @ts-ignore
  import SolarHealthOutline from "~icons/solar/health-outline";
  // @ts-ignore
  import SolarDocumentTextOutline from "~icons/solar/document-text-outline";
  // @ts-ignore
  import SolarLinkOutline from "~icons/solar/link-outline";
  // @ts-ignore
  import SolarCloseCircleOutline from "~icons/solar/close-circle-outline";

  import IconWOW from "$lib/assets/images/logo_wow_new.svg";
  import IconSyndose from "$lib/assets/images/logo_syndose.svg";
  import IconWDM from "$lib/assets/images/logo_wdm_new.svg";
  import IconAI from "$lib/assets/images/logo_ai_new.svg"

  export let show: boolean = false;

  const closeModal = () => {
    show = false;
  };

  const handleBackdropClick = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  };

  const loginOptions = [
    {
      title: m.login_modal_diag_title(),
      description: m.login_modal_diag_desc(),
      url: "https://diagnostyka.zbadani.pl",
      icon: IconWOW,
      icon2: IconWDM,
      icon3: IconAI
    },
    {
      title: m.login_modal_syndose_title(),
      description: m.login_modal_syndose_desc(),
      url: "https://syndose.zbadani.pl",
      icon: IconSyndose,
      icon2: ''
    }
  ];
</script>

{#if show}
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 bg-blue-900"
    on:click={handleBackdropClick}
    on:keydown={(e) => e.key === 'Escape' && closeModal()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
  >
    <div class="bg-white rounded-2xl shadow-2xl w-full sm:min-w-[640px] max-w-[720px] max-h-[90vh] overflow-y-auto border-[5px] border-[#eee]">
      <!-- Header -->
      <div class="flex items-center justify-between p-6">
        <h2 id="modal-title" class="text-2xl font-medium text-blue-300 sm:block hidden">
          {m.login_modal_title()}
        </h2>
        <button
          on:click={closeModal}
          class="w-12 h-12 bg-blue-900 rounded-full flex items-center justify-center text-blue-300 hover:text-blue-500 transition-colors"
          aria-label="Zamknij"
        >
          <SolarCloseCircleOutline class="text-3xl" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 pt-0">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-0">
          {#each loginOptions as option, index}
          <a
            href={option.url}
            target="_blank"
            class="flex align-items justify-center items-center p-4 bg-white hover:bg-[rgba(238,242,252,0.5)] border border-blue-800 text-center {index === 0 ? 'sm:rounded-l-xl sm:border-r-0 sm:rounded-r-none rounded-xl' : ''} {index === loginOptions.length - 1 ? 'sm:rounded-r-xl sm:rounded-l-none rounded-xl' : ''}"
            on:click={closeModal}
          >
            <div class="flex flex-col items-center space-y-4">
              <div>
                <div class="flex flex-row my-2 gap-2">
                  <img src="{option.icon}" alt="Icon" class="mx-auto text-center mb-4 max-w-12" />
                  {#if option.icon2}
                    <img src="{option.icon2}" alt="Icon" class="mx-auto text-center mb-4 max-w-12" />
                  {/if}
                  {#if option.icon3}
                  <img src="{option.icon3}" alt="Icon" class="mx-auto text-center mb-4 max-w-12" />
                {/if}
                </div>
                <h3 class="font-base text-center text-blue-500 font-medium text-base">
                  {option.title}
                </h3>
                <p class="text-base">
                  {option.description}
                </p>
              </div>
            </div>
          </a>
          {/each}
        </div>
      </div>
    </div>
  </div>
{/if}

<style lang="postcss">
  /* Responsive adjustments for mobile */
  @media (max-width: 768px) {
    .grid {
      @apply grid-cols-1 gap-3;
    }

    .w-16.h-16 {
      @apply w-12 h-12;
    }
  }
</style>
