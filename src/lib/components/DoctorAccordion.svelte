<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
    import type { SvelteComponent } from 'svelte';
	import clsx, { type ClassValue } from 'clsx'
	import { twMerge } from 'tailwind-merge';

	import { createAccordion, melt } from '@melt-ui/svelte';
	import { slide } from 'svelte/transition';

	import { fade } from 'svelte/transition';

	// @ts-ignore
    import SolarAltArrowDownOutline from '~icons/solar/alt-arrow-down-outline'
	// @ts-ignore
    import SolarAltArrowUpOutline from '~icons/solar/alt-arrow-up-outline'
	// @ts-ignore
    import SolarHealthOutline from '~icons/solar/health-outline'
	// @ts-ignore
    import SolarDocumentTextOutline from '~icons/solar/document-text-outline'
	// @ts-ignore
    import SolarLogin3Outline from '~icons/solar/login-3-outline'
	// @ts-ignore
    import SolarLaptopMinimalisticOutline from '~icons/solar/laptop-minimalistic-outline'

	import DoctorImg1 from '$lib/assets/images/doctor_img_1.webp'
	import DoctorImg2 from '$lib/assets/images/doctor_img_2.webp'
	import DoctorImg3 from '$lib/assets/images/doctor_img_3.webp'
	import DoctorImg4 from '$lib/assets/images/doctor_img_4.webp'

	const images = [
		{ id: 1, src: DoctorImg1 },
		{ id: 2, src: DoctorImg2 },
		{ id: 3, src: DoctorImg3 },
		{ id: 4, src: DoctorImg4 },
	];

	function cn(...classes: ClassValue[]) {
		return twMerge(clsx(classes))
	}

	let selectedAccordionItem: any = 'item-1';

	const {
		elements: { content, item, trigger, root },
		helpers: { isSelected },
	} = createAccordion({
		defaultValue: 'item-1',
		onValueChange: ({ curr, next }) => {
			selectedAccordionItem = next;
			return next
		},
	});

    interface Item {
        id: string;
        icon: typeof SvelteComponent;
        title: any;
        description: any;
    }

	const items: Item[] = [
		{
			id: 'item-1',
            icon: SolarDocumentTextOutline,
			title: m.doctora_1_title(),
			description: m.doctora_1_description(),
		},
		{
			id: 'item-2',
            icon: SolarLogin3Outline,
			title: m.doctora_2_title(),
			description: m.doctora_2_description(),
		},
	];

	let className = '';
	export { className as class };
</script>

<div class="lg:grid grid-cols-2 gap-20">
	<div class="flex items-center w-full">
		{#each items as { id }, i}
			{#if $isSelected(id)}
				<img in:fade={{ delay: 50, duration: 300 }} width="680" height="600" class="rounded-xl" src={images[i].src}  alt="Dla lekarza {i + 1}" />
			{/if}
		{/each}
	</div>
	<div>
		<div
		class={cn(
			'my-16',
			className,
		)}
		{...$root}
		>
		{#each items as { id, title, description, icon }, i}
			<div
			use:melt={$item(id)}
			class="mb-4"
			>
			<h2 class="flex">
				<button
				use:melt={$trigger(id)}
				class={cn(
					'flex flex-1 cursor-pointer items-center',
					'border-2 border-blue-900 rounded-xl hover:border-blue-500 hover:text-blue-500',
					'px-6 pt-6 pb-6',
                    'text-blue-300 font-semibold underline',
                    $isSelected(id) && 'hover:border-transparent no-underline bg-blue-900 rounded-t-xl rounded-b-none'
				)}
				>
                <svelte:component this={icon} class="text-2xl mr-4" />
				{title}
                {#if $isSelected(id)}<SolarAltArrowUpOutline class="ml-auto text-2xl" />{:else}<SolarAltArrowDownOutline class="text-2xl ml-auto opacity-25" />{/if}
				</button>
			</h2>
			{#if $isSelected(id)}
				<div
				class={cn(
					'text-blue-300 bg-blue-900 rounded-b-xl',
				)}
				use:melt={$content(id)}
				transition:slide
				>
				<div class="px-6 pb-6">
					{description}
				</div>
				</div>
			{/if}
			</div>
		{/each}
		</div>

	</div>
</div>