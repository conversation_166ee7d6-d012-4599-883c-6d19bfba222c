/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  darkMode: ['class'],
  theme: {
    screens: {
      sm: '640px',
      md: '1024px',
      lg: '1180px',
      xl: '1366px',
    },
    fontFamily: {
      sans: ['Lexend', 'Rubik', 'sans-serif']
    },
    extend: {
      spacing: {
        '128': '32rem',
        '144': '36rem',
      },
      borderRadius: {
        '4xl': '2rem',
      }},
    colors: {
      'white': '#ffffff',
      'transparent': 'transparent',
      'primary': '#0848ff',
      'light': '#eef2fc',
      'blue': {
        100: '#001552',
        300: '#002aa3',
        500: '#0848ff',
        600: '#2960FF',
        700: '#7e9cf1',
        800: '#E6ECFF',
        900: '#EEF6FC',
      },
      'red': {
        100: '#701030',
        300: '#ad1f4e',
        500: '#e5195d',
        700: '#f4719d',
        900: '#fbe9ef'
      },
      'green': {
        100: '#15654a',
        300: '#288a6a',
        500: '#1dc98f',
        700: '#99e5cc',
        900: '#ddf8ef'
      },
      'orange': {
        100: '#854200',
        300: '#e57300',
        500: '#ff6216',
        700: '#ffbf80',
        900: '#ff621619'
      },
      'black': {
        100: '#000000',
        300: '#4c4c4c',
        500: '#7f7f7f',
        700: '#b2b2b2',
        900: '#bfbfbf',
      }
    }
  },
  plugins: [
    require("@tailwindcss/typography"),
    require('tailwind-hamburgers')
  ]
}