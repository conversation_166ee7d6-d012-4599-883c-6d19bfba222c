import { paraglide } from '@inlang/paraglide-sveltekit/vite'
import { sveltekit } from '@sveltejs/kit/vite';
import { enhancedImages } from '@sveltejs/enhanced-img';
import { defineConfig } from 'vite';
import Icons from 'unplugin-icons/vite'

export default defineConfig({
	plugins: [
		paraglide({ 
			project: './project.inlang', 
			outdir: './src/lib/paraglide' 
	}),
	Icons({
        autoInstall: true,
		compiler: 'svelte',
	}),
	enhancedImages(),
	sveltekit(),
	]
});
