import { preprocessMeltUI } from '@melt-ui/pp';
import sequence from 'svelte-sequential-preprocessor';
import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';
/** @type {import('@sveltejs/kit').Config}*/
const config = {
	// Consult https://kit.svelte.dev/docs/integrations#preprocessors
	// for more information about preprocessors
	preprocess: sequence([vitePreprocess(), preprocessMeltUI()]),
	kit: {
		// adapter-auto only supports some environments, see https://kit.svelte.dev/docs/adapter-auto for a list.
		// If your environment is not supported or you settled on a specific environment, switch out the adapter.
		// See https://kit.svelte.dev/docs/adapters for more information about adapters.
		adapter: adapter(),
		alias: {
			// You can call this whatever you want
			$lib: './src/lib',
			$paraglide: './src/lib/paraglide'
		},
		prerender: {
			origin: "http://localhost:5173",
			entries: [
				'/',
				'/articles/',
				'/articles/webinar-pytania-odpowiedzi-2024-02-29/',
				'/dla-profesjonalistow/',
				'/dla-profesjonalistow/produkty/syndose/',
				'/dla-profesjonalistow/produkty/wow/',
				'/dla-profesjonalistow/produkty/wdm/',
				'/demo',
				'/demo/prezentacja'
			]
		}
	},
	vitePlugin: {
		inspector: false
	}
};
export default config;
